<!DOCTYPE html>
<html lang="fr">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>EasyBus - Réservation de billets de bus en Afrique de l'Ouest</title>
    <link rel="stylesheet" href="/css/bootstrap.min.css"/>
    <link rel="icon" type="image/svg+xml" href="favicon.svg">
    <link rel="icon" type="image/png" href="/favicon.png">
    <style>
      :root {
        --primary-color: #0d6efd;
        --secondary-color: #f8f9fa;
        --accent-color: #ffc107;
        --dark-color: #212529;
      }

      body {
        font-family: "Segoe UI", Tahoma, Geneva, Verdana, sans-serif;
        background-color: #f5f5f5;
      }

      /* Hero section styles */
      .hero-section {
        height: 60vh;
        display: flex;
        align-items: center;
        color: white;
        position: relative;
        overflow: hidden;
      }

      .hero-section::before {
        content: "";
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: linear-gradient(rgba(0, 0, 0, 0.5), rgba(0, 0, 0, 0.5)),
          /* Dégradé noir avec opacité */ url("/img/easybus.webp") center/cover
            no-repeat;
        z-index: -1; /* Place l'image floutée derrière le contenu */
      }

      .hero-content {
        text-align: center;
        max-width: 800px;
        margin: 0 auto;
        padding: 0 20px;
        z-index: 1;
      }

      .hero-heading {
        font-size: 3rem;
        font-weight: 700;
        margin-bottom: 1rem;
        text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
      }

      .hero-subheading {
        font-size: 1.5rem;
        margin-bottom: 2rem;
        text-shadow: 1px 1px 3px rgba(0, 0, 0, 0.5);
      }

      /* Search form styles */
      .search-form-container {
        margin-top: -70px;
        position: relative;
        z-index: 10;
      }

      .search-form {
        background-color: white;
        border-radius: 15px;
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
        padding: 30px;
      }

      .search-form .form-control {
        border-radius: 8px;
        padding: 12px 15px;
        font-size: 1rem;
        border: 1px solid #dee2e6;
      }

      .search-form .form-control:focus {
        box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.15);
        border-color: var(--primary-color);
      }

      .search-btn {
        border-radius: 8px;
        padding: 12px 25px;
        font-weight: 600;
        font-size: 1rem;
        background-color: var(--primary-color);
        border-color: var(--primary-color);
        transition: all 0.3s;
      }

      .search-btn:hover {
        transform: translateY(-2px);
        box-shadow: 0 5px 15px rgba(13, 110, 253, 0.3);
      }

      .city-dropdown {
        position: absolute;
        z-index: 1000;
        width: 100%;
        max-height: 300px;
        overflow-y: auto;
        background-color: white;
        border-radius: 8px;
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
        display: none;
      }

      .city-dropdown.show {
        display: block;
      }

      .city-item {
        padding: 12px 15px;
        border-bottom: 1px solid #f0f0f0;
        cursor: pointer;
        transition: background-color 0.2s;
      }

      .city-item:hover {
        background-color: #f8f9fa;
      }

      .city-item:last-child {
        border-bottom: none;
      }

      .city-name {
        font-weight: 600;
        font-size: 1rem;
      }

      .city-details {
        font-size: 0.8rem;
        color: #6c757d;
      }

      /* Banner search form styles */
      .search-banner {
        background: linear-gradient(135deg, var(--primary-color), #0052cc);
        padding: 20px 0;
        margin-bottom: 30px;
      }

      .search-banner .search-form {
        background-color: transparent;
        box-shadow: none;
        padding: 0;
      }

      .search-banner .form-control {
        background-color: rgba(255, 255, 255, 0.9);
        border: none;
      }

      .search-banner .form-label {
        color: white;
        font-weight: 600;
      }

      .search-banner .search-btn {
        background-color: var(--accent-color);
        border-color: var(--accent-color);
        color: var(--dark-color);
      }

      /* Main content sections */
      .section-title {
        font-size: 2.2rem;
        font-weight: 700;
        text-align: center;
        margin-bottom: 3rem;
        position: relative;
        padding-bottom: 15px;
      }

      .section-title:after {
        content: "";
        position: absolute;
        bottom: 0;
        left: 50%;
        transform: translateX(-50%);
        width: 80px;
        height: 3px;
        background-color: var(--primary-color);
      }

      .feature-card {
        background-color: white;
        border-radius: 12px;
        padding: 30px;
        text-align: center;
        margin-bottom: 30px;
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
        transition: transform 0.3s, box-shadow 0.3s;
      }

      .feature-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 15px 30px rgba(0, 0, 0, 0.1);
      }

      .feature-icon {
        width: 70px;
        height: 70px;
        border-radius: 50%;
        background-color: rgba(13, 110, 253, 0.1);
        display: flex;
        align-items: center;
        justify-content: center;
        margin: 0 auto 20px;
        color: var(--primary-color);
        font-size: 1.8rem;
      }

      .feature-title {
        font-size: 1.3rem;
        font-weight: 600;
        margin-bottom: 15px;
      }

      .route-card {
        display: flex;
        background-color: white;
        border-radius: 12px;
        overflow: hidden;
        margin-bottom: 20px;
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
        transition: transform 0.3s, box-shadow 0.3s;
      }

      .route-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 15px 30px rgba(0, 0, 0, 0.1);
      }

      .route-image {
        width: 150px;
        background-color: #ccc;
      }

      .route-content {
        padding: 20px;
        flex-grow: 1;
      }

      .route-price {
        font-size: 1.5rem;
        font-weight: 700;
        color: var(--primary-color);
      }

      .testimonial-card {
        background-color: white;
        border-radius: 12px;
        padding: 30px;
        margin-bottom: 30px;
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
        transition: transform 0.3s, box-shadow 0.3s;
      }

      .testimonial-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 15px 30px rgba(0, 0, 0, 0.1);
      }

      .testimonial-avatar {
        width: 60px;
        height: 60px;
        border-radius: 50%;
        margin-right: 15px;
        background-color: #ccc;
      }

      .testimonial-rating {
        color: var(--accent-color);
        margin-bottom: 15px;
      }

      footer {
        background-color: var(--dark-color);
        color: white;
        padding: 60px 0 30px;
      }

      .footer-title {
        font-size: 1.2rem;
        font-weight: 600;
        margin-bottom: 20px;
        padding-bottom: 10px;
        border-bottom: 2px solid rgba(255, 255, 255, 0.1);
      }

      .footer-links {
        list-style: none;
        padding: 0;
        margin: 0;
      }

      .footer-links li {
        margin-bottom: 10px;
      }

      .footer-links a {
        color: rgba(255, 255, 255, 0.7);
        text-decoration: none;
        transition: color 0.2s;
      }

      .footer-links a:hover {
        color: white;
      }

      .social-links {
        margin-top: 20px;
      }

      .social-links a {
        color: white;
        background-color: rgba(255, 255, 255, 0.1);
        width: 36px;
        height: 36px;
        border-radius: 50%;
        display: inline-flex;
        align-items: center;
        justify-content: center;
        margin-right: 10px;
        transition: background-color 0.2s;
      }

      .social-links a:hover {
        background-color: var(--primary-color);
      }

      .footer-bottom {
        border-top: 1px solid rgba(255, 255, 255, 0.1);
        padding-top: 20px;
        margin-top: 40px;
      }

      @media (max-width: 768px) {
        .hero-heading {
          font-size: 2rem;
        }

        .hero-subheading {
          font-size: 1.2rem;
        }

        .search-form-container {
          margin-top: -45px;
        }

        .route-card {
          flex-direction: column;
        }

        .route-image {
          width: 100%;
          height: 150px;
        }

        .hero-section {
          height: 30vh;
        }
      }
    </style>
  </head>

  <body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-light bg-white shadow-sm">
      <div class="container">
        <a class="navbar-brand fw-bold" href="#">
          <svg class="text-primary me-2" fill="currentColor" width="1.5em" height="1.5em" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 576 512"><path d="M288 0C422.4 0 512 35.2 512 80l0 16 0 32c17.7 0 32 14.3 32 32l0 64c0 17.7-14.3 32-32 32l0 160c0 17.7-14.3 32-32 32l0 32c0 17.7-14.3 32-32 32l-32 0c-17.7 0-32-14.3-32-32l0-32-192 0 0 32c0 17.7-14.3 32-32 32l-32 0c-17.7 0-32-14.3-32-32l0-32c-17.7 0-32-14.3-32-32l0-160c-17.7 0-32-14.3-32-32l0-64c0-17.7 14.3-32 32-32c0 0 0 0 0 0l0-32s0 0 0 0l0-16C64 35.2 153.6 0 288 0zM128 160l0 96c0 17.7 14.3 32 32 32l112 0 0-160-112 0c-17.7 0-32 14.3-32 32zM304 288l112 0c17.7 0 32-14.3 32-32l0-96c0-17.7-14.3-32-32-32l-112 0 0 160zM144 400a32 32 0 1 0 0-64 32 32 0 1 0 0 64zm288 0a32 32 0 1 0 0-64 32 32 0 1 0 0 64zM384 80c0-8.8-7.2-16-16-16L208 64c-8.8 0-16 7.2-16 16s7.2 16 16 16l160 0c8.8 0 16-7.2 16-16z"/></svg>EasyBus
        </a>
        <button
          class="navbar-toggler"
          type="button"
          data-bs-toggle="collapse"
          data-bs-target="#navbarNav"
        >
          <span class="navbar-toggler-icon"></span>
        </button>
        <div class="collapse navbar-collapse" id="navbarNav">
          <ul class="navbar-nav ms-auto">
            <li class="nav-item">
              <a class="nav-link active" href="#">Accueil</a>
            </li>
            <li class="nav-item">
              <a class="nav-link" href="#">Destinations</a>
            </li>
            <li class="nav-item">
              <a class="nav-link" href="#">Services</a>
            </li>
            <li class="nav-item">
              <a class="nav-link" href="#">Compagnies</a>
            </li>
            <li class="nav-item">
              <a class="nav-link" href="#">Contact</a>
            </li>
          </ul>
          <div class="ms-lg-3 mt-3 mt-lg-0">
            <a href="#" class="btn btn-outline-primary me-2">Se connecter</a>
            <a href="#" class="btn btn-primary">S'inscrire</a>
          </div>
        </div>
      </div>
    </nav>

    <!-- Hero Section -->
    <section class="hero-section">
      <div class="hero-content">
        <h1 class="hero-heading">Voyagez en toute simplicité</h1>
        <p class="hero-subheading">
          Réservez vos billets de bus pour l'Afrique de l'Ouest en quelques
          clics
        </p>
      </div>
    </section>

    <!-- Search Form -->
    <div class="container search-form-container">
      <div class="search-form">
        <form
          id="trip-search-form"
          method="GET"
          action="https://bus-booking.test/search.html"
        >
          <div class="row g-3">
            <div class="col-md-4 position-relative">
              <label for="departure" class="form-label">Lieu de départ</label>
              <input
                type="text"
                class="form-control"
                id="departure"
                placeholder="D'où partez-vous ?"
                autocomplete="off"
              />
              <input type="hidden" id="departure-id" name="from" />
              <div class="city-dropdown" id="departure-dropdown"></div>
            </div>
            <div class="col-md-4 position-relative">
              <label for="destination" class="form-label"
                >Lieu de destination</label
              >
              <input
                type="text"
                class="form-control"
                id="destination"
                placeholder="Où allez-vous ?"
                autocomplete="off"
              />
              <input type="hidden" id="destination-id" name="to" />
              <div class="city-dropdown" id="destination-dropdown"></div>
            </div>
            <div class="col-md-2">
              <label for="departure-date" class="form-label"
                >Date de départ</label
              >
              <input
                type="date"
                class="form-control"
                id="departure-date"
                name="date"
                min=""
                value=""
              />
            </div>
            <div class="col-md-2 d-flex align-items-end">
              <button type="submit" class="btn btn-primary search-btn w-100">
                <svg class="me-2" xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" class="bi bi-search" viewBox="0 0 512 512"><path d="M416 208c0 45.9-14.9 88.3-40 122.7L502.6 457.4c12.5 12.5 12.5 32.8 0 45.3s-32.8 12.5-45.3 0L330.7 376c-34.4 25.2-76.8 40-122.7 40C93.1 416 0 322.9 0 208S93.1 0 208 0S416 93.1 416 208zM208 352a144 144 0 1 0 0-288 144 144 0 1 0 0 288z"/></svg>Rechercher
              </button>
            </div>
          </div>
        </form>
      </div>
    </div>

    <!-- Popular Routes Section -->
    <section class="py-5 mt-5">
      <div class="container">
        <h2 class="section-title">Destinations populaires</h2>
        <div class="row">
          <div class="col-md-6">
            <div class="route-card">
              <div class="route-image">
                <img
                  src="/img/parakou.jpg"
                  alt="Parakou"
                  class="img-fluid h-100 w-100 object-fit-cover"
                />
              </div>
              <div
                class="route-content d-flex justify-content-between align-items-center"
              >
                <div>
                  <h3 class="fs-5 fw-bold mb-2">Cotonou → Parakou</h3>
                  <p class="mb-1 d-flex align-items-center">
                    <svg class="text-primary me-2" xmlns="http://www.w3.org/2000/svg" fill="currentColor" width="18" height="16" viewBox="0 0 576 512"><path d="M256 32l-74.8 0c-27.1 0-51.3 17.1-60.3 42.6L3.1 407.2C1.1 413 0 419.2 0 425.4C0 455.5 24.5 480 54.6 480L256 480l0-64c0-17.7 14.3-32 32-32s32 14.3 32 32l0 64 201.4 0c30.2 0 54.6-24.5 54.6-54.6c0-6.2-1.1-12.4-3.1-18.2L455.1 74.6C446 49.1 421.9 32 394.8 32L320 32l0 64c0 17.7-14.3 32-32 32s-32-14.3-32-32l0-64zm64 192l0 64c0 17.7-14.3 32-32 32s-32-14.3-32-32l0-64c0-17.7 14.3-32 32-32s32 14.3 32 32z"/></svg>
                    418 km - 7h
                  </p>
                  <p class="mb-0 d-flex align-items-center">
                    <svg class="text-primary me-2" xmlns="http://www.w3.org/2000/svg" fill="currentColor" width="16" height="16"" viewBox="0 0 512 512"><path d="M128 0c17.7 0 32 14.3 32 32l0 32 128 0 0-32c0-17.7 14.3-32 32-32s32 14.3 32 32l0 32 48 0c26.5 0 48 21.5 48 48l0 48L0 160l0-48C0 85.5 21.5 64 48 64l48 0 0-32c0-17.7 14.3-32 32-32zM0 192l448 0 0 272c0 26.5-21.5 48-48 48L48 512c-26.5 0-48-21.5-48-48L0 192zm64 80l0 32c0 8.8 7.2 16 16 16l32 0c8.8 0 16-7.2 16-16l0-32c0-8.8-7.2-16-16-16l-32 0c-8.8 0-16 7.2-16 16zm128 0l0 32c0 8.8 7.2 16 16 16l32 0c8.8 0 16-7.2 16-16l0-32c0-8.8-7.2-16-16-16l-32 0c-8.8 0-16 7.2-16 16zm144-16c-8.8 0-16 7.2-16 16l0 32c0 8.8 7.2 16 16 16l32 0c8.8 0 16-7.2 16-16l0-32c0-8.8-7.2-16-16-16l-32 0zM64 400l0 32c0 8.8 7.2 16 16 16l32 0c8.8 0 16-7.2 16-16l0-32c0-8.8-7.2-16-16-16l-32 0c-8.8 0-16 7.2-16 16zm144-16c-8.8 0-16 7.2-16 16l0 32c0 8.8 7.2 16 16 16l32 0c8.8 0 16-7.2 16-16l0-32c0-8.8-7.2-16-16-16l-32 0zm112 16l0 32c0 8.8 7.2 16 16 16l32 0c8.8 0 16-7.2 16-16l0-32c0-8.8-7.2-16-16-16l-32 0c-8.8 0-16 7.2-16 16z"/></svg>
                    Lun, Mer, Ven, Sam
                  </p>
                </div>
                <div class="text-end">
                  <div class="route-price mb-2">6 500 FCFA</div>
                  <a href="#" class="btn btn-sm btn-outline-primary"
                    >Voir les horaires</a
                  >
                </div>
              </div>
            </div>
          </div>
          <div class="col-md-6">
            <div class="route-card">
              <div class="route-image">
                <img
                  src="/img/cotonou.jpg"
                  alt="Cotonou"
                  class="img-fluid h-100 w-100 object-fit-cover"
                />
              </div>
              <div
                class="route-content d-flex justify-content-between align-items-center"
              >
                <div>
                  <h3 class="fs-5 fw-bold mb-2">Abidjan → Cotonou</h3>
                  <p class="mb-1 d-flex align-items-center">
                    <svg class="text-primary me-2" xmlns="http://www.w3.org/2000/svg" fill="currentColor" width="18" height="16" viewBox="0 0 576 512"><path d="M256 32l-74.8 0c-27.1 0-51.3 17.1-60.3 42.6L3.1 407.2C1.1 413 0 419.2 0 425.4C0 455.5 24.5 480 54.6 480L256 480l0-64c0-17.7 14.3-32 32-32s32 14.3 32 32l0 64 201.4 0c30.2 0 54.6-24.5 54.6-54.6c0-6.2-1.1-12.4-3.1-18.2L455.1 74.6C446 49.1 421.9 32 394.8 32L320 32l0 64c0 17.7-14.3 32-32 32s-32-14.3-32-32l0-64zm64 192l0 64c0 17.7-14.3 32-32 32s-32-14.3-32-32l0-64c0-17.7 14.3-32 32-32s32 14.3 32 32z"/></svg>
                    867 km - 28h30
                  </p>
                  <p class="mb-0 d-flex align-items-center">
                    <svg class="text-primary me-2" xmlns="http://www.w3.org/2000/svg" fill="currentColor" width="16" height="16"" viewBox="0 0 512 512"><path d="M128 0c17.7 0 32 14.3 32 32l0 32 128 0 0-32c0-17.7 14.3-32 32-32s32 14.3 32 32l0 32 48 0c26.5 0 48 21.5 48 48l0 48L0 160l0-48C0 85.5 21.5 64 48 64l48 0 0-32c0-17.7 14.3-32 32-32zM0 192l448 0 0 272c0 26.5-21.5 48-48 48L48 512c-26.5 0-48-21.5-48-48L0 192zm64 80l0 32c0 8.8 7.2 16 16 16l32 0c8.8 0 16-7.2 16-16l0-32c0-8.8-7.2-16-16-16l-32 0c-8.8 0-16 7.2-16 16zm128 0l0 32c0 8.8 7.2 16 16 16l32 0c8.8 0 16-7.2 16-16l0-32c0-8.8-7.2-16-16-16l-32 0c-8.8 0-16 7.2-16 16zm144-16c-8.8 0-16 7.2-16 16l0 32c0 8.8 7.2 16 16 16l32 0c8.8 0 16-7.2 16-16l0-32c0-8.8-7.2-16-16-16l-32 0zM64 400l0 32c0 8.8 7.2 16 16 16l32 0c8.8 0 16-7.2 16-16l0-32c0-8.8-7.2-16-16-16l-32 0c-8.8 0-16 7.2-16 16zm144-16c-8.8 0-16 7.2-16 16l0 32c0 8.8 7.2 16 16 16l32 0c8.8 0 16-7.2 16-16l0-32c0-8.8-7.2-16-16-16l-32 0zm112 16l0 32c0 8.8 7.2 16 16 16l32 0c8.8 0 16-7.2 16-16l0-32c0-8.8-7.2-16-16-16l-32 0c-8.8 0-16 7.2-16 16z"/></svg>
                    Départs quotidiens
                  </p>
                </div>
                <div class="text-end">
                  <div class="route-price mb-2">35 000 FCFA</div>
                  <a href="#" class="btn btn-sm btn-outline-primary"
                    >Voir les horaires</a
                  >
                </div>
              </div>
            </div>
          </div>
          <div class="col-md-6">
            <div class="route-card">
              <div class="route-image">
                <img
                  src="/img/nati.jpg"
                  alt="Natitingou"
                  class="img-fluid h-100 w-100 object-fit-cover"
                />
              </div>
              <div
                class="route-content d-flex justify-content-between align-items-center"
              >
                <div>
                  <h3 class="fs-5 fw-bold mb-2">Cotonou → Natitingou</h3>
                  <p class="mb-1 d-flex align-items-center">
                    <svg class="text-primary me-2" xmlns="http://www.w3.org/2000/svg" fill="currentColor" width="18" height="16" viewBox="0 0 576 512"><path d="M256 32l-74.8 0c-27.1 0-51.3 17.1-60.3 42.6L3.1 407.2C1.1 413 0 419.2 0 425.4C0 455.5 24.5 480 54.6 480L256 480l0-64c0-17.7 14.3-32 32-32s32 14.3 32 32l0 64 201.4 0c30.2 0 54.6-24.5 54.6-54.6c0-6.2-1.1-12.4-3.1-18.2L455.1 74.6C446 49.1 421.9 32 394.8 32L320 32l0 64c0 17.7-14.3 32-32 32s-32-14.3-32-32l0-64zm64 192l0 64c0 17.7-14.3 32-32 32s-32-14.3-32-32l0-64c0-17.7 14.3-32 32-32s32 14.3 32 32z"/></svg>
                    540 km - 10h
                  </p>
                  <p class="mb-0 d-flex align-items-center">
                    <svg class="text-primary me-2" xmlns="http://www.w3.org/2000/svg" fill="currentColor" width="16" height="16"" viewBox="0 0 512 512"><path d="M128 0c17.7 0 32 14.3 32 32l0 32 128 0 0-32c0-17.7 14.3-32 32-32s32 14.3 32 32l0 32 48 0c26.5 0 48 21.5 48 48l0 48L0 160l0-48C0 85.5 21.5 64 48 64l48 0 0-32c0-17.7 14.3-32 32-32zM0 192l448 0 0 272c0 26.5-21.5 48-48 48L48 512c-26.5 0-48-21.5-48-48L0 192zm64 80l0 32c0 8.8 7.2 16 16 16l32 0c8.8 0 16-7.2 16-16l0-32c0-8.8-7.2-16-16-16l-32 0c-8.8 0-16 7.2-16 16zm128 0l0 32c0 8.8 7.2 16 16 16l32 0c8.8 0 16-7.2 16-16l0-32c0-8.8-7.2-16-16-16l-32 0c-8.8 0-16 7.2-16 16zm144-16c-8.8 0-16 7.2-16 16l0 32c0 8.8 7.2 16 16 16l32 0c8.8 0 16-7.2 16-16l0-32c0-8.8-7.2-16-16-16l-32 0zM64 400l0 32c0 8.8 7.2 16 16 16l32 0c8.8 0 16-7.2 16-16l0-32c0-8.8-7.2-16-16-16l-32 0c-8.8 0-16 7.2-16 16zm144-16c-8.8 0-16 7.2-16 16l0 32c0 8.8 7.2 16 16 16l32 0c8.8 0 16-7.2 16-16l0-32c0-8.8-7.2-16-16-16l-32 0zm112 16l0 32c0 8.8 7.2 16 16 16l32 0c8.8 0 16-7.2 16-16l0-32c0-8.8-7.2-16-16-16l-32 0c-8.8 0-16 7.2-16 16z"/></svg>
                    Départs quotidiens
                  </p>
                </div>
                <div class="text-end">
                  <div class="route-price mb-2">8 500 FCFA</div>
                  <a href="#" class="btn btn-sm btn-outline-primary"
                    >Voir les horaires</a
                  >
                </div>
              </div>
            </div>
          </div>
          <div class="col-md-6">
            <div class="route-card">
              <div class="route-image">
                <img
                  src="/img/abidjan.jpg"
                  alt="Abidjan"
                  class="img-fluid h-100 w-100 object-fit-cover"
                />
              </div>
              <div
                class="route-content d-flex justify-content-between align-items-center"
              >
                <div>
                  <h3 class="fs-5 fw-bold mb-2">Cotonou → Abidjan</h3>
                  <p class="mb-1 d-flex align-items-center">
                    <svg class="text-primary me-2" xmlns="http://www.w3.org/2000/svg" fill="currentColor" width="18" height="16" viewBox="0 0 576 512"><path d="M256 32l-74.8 0c-27.1 0-51.3 17.1-60.3 42.6L3.1 407.2C1.1 413 0 419.2 0 425.4C0 455.5 24.5 480 54.6 480L256 480l0-64c0-17.7 14.3-32 32-32s32 14.3 32 32l0 64 201.4 0c30.2 0 54.6-24.5 54.6-54.6c0-6.2-1.1-12.4-3.1-18.2L455.1 74.6C446 49.1 421.9 32 394.8 32L320 32l0 64c0 17.7-14.3 32-32 32s-32-14.3-32-32l0-64zm64 192l0 64c0 17.7-14.3 32-32 32s-32-14.3-32-32l0-64c0-17.7 14.3-32 32-32s32 14.3 32 32z"/></svg>
                    867 km - 28h30
                  </p>
                  <p class="mb-0 d-flex align-items-center">
                    <svg class="text-primary me-2" xmlns="http://www.w3.org/2000/svg" fill="currentColor" width="16" height="16"" viewBox="0 0 512 512"><path d="M128 0c17.7 0 32 14.3 32 32l0 32 128 0 0-32c0-17.7 14.3-32 32-32s32 14.3 32 32l0 32 48 0c26.5 0 48 21.5 48 48l0 48L0 160l0-48C0 85.5 21.5 64 48 64l48 0 0-32c0-17.7 14.3-32 32-32zM0 192l448 0 0 272c0 26.5-21.5 48-48 48L48 512c-26.5 0-48-21.5-48-48L0 192zm64 80l0 32c0 8.8 7.2 16 16 16l32 0c8.8 0 16-7.2 16-16l0-32c0-8.8-7.2-16-16-16l-32 0c-8.8 0-16 7.2-16 16zm128 0l0 32c0 8.8 7.2 16 16 16l32 0c8.8 0 16-7.2 16-16l0-32c0-8.8-7.2-16-16-16l-32 0c-8.8 0-16 7.2-16 16zm144-16c-8.8 0-16 7.2-16 16l0 32c0 8.8 7.2 16 16 16l32 0c8.8 0 16-7.2 16-16l0-32c0-8.8-7.2-16-16-16l-32 0zM64 400l0 32c0 8.8 7.2 16 16 16l32 0c8.8 0 16-7.2 16-16l0-32c0-8.8-7.2-16-16-16l-32 0c-8.8 0-16 7.2-16 16zm144-16c-8.8 0-16 7.2-16 16l0 32c0 8.8 7.2 16 16 16l32 0c8.8 0 16-7.2 16-16l0-32c0-8.8-7.2-16-16-16l-32 0zm112 16l0 32c0 8.8 7.2 16 16 16l32 0c8.8 0 16-7.2 16-16l0-32c0-8.8-7.2-16-16-16l-32 0c-8.8 0-16 7.2-16 16z"/></svg>
                    Mar, Jeu, Dim
                  </p>
                </div>
                <div class="text-end">
                  <div class="route-price mb-2">35 000 FCFA</div>
                  <a href="#" class="btn btn-sm btn-outline-primary"
                    >Voir les horaires</a
                  >
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="text-center mt-4">
          <a href="#" class="btn btn-outline-primary"
            >Voir toutes les destinations</a
          >
        </div>
      </div>
    </section>

    <!-- Features Section -->
    <section class="py-5 bg-light">
      <div class="container">
        <h2 class="section-title">Pourquoi nous choisir</h2>
        <div class="row">
          <div class="col-md-4">
            <div class="feature-card">
              <div class="feature-icon">
                <svg xmlns="http://www.w3.org/2000/svg" fill="currentColor" width="30" height="30" viewBox="0 0 576 512"><path d="M0 128C0 92.7 28.7 64 64 64l448 0c35.3 0 64 28.7 64 64l0 64c0 8.8-7.4 15.7-15.7 18.6C541.5 217.1 528 235 528 256s13.5 38.9 32.3 45.4c8.3 2.9 15.7 9.8 15.7 18.6l0 64c0 35.3-28.7 64-64 64L64 448c-35.3 0-64-28.7-64-64l0-64c0-8.8 7.4-15.7 15.7-18.6C34.5 294.9 48 277 48 256s-13.5-38.9-32.3-45.4C7.4 207.7 0 200.8 0 192l0-64z"/></svg>
              </div>
              <h3 class="feature-title">Réservation simple</h3>
              <p>
                Réservez vos billets de bus en quelques clics sans vous déplacer
                ni faire la queue.
              </p>
            </div>
          </div>
          <div class="col-md-4">
            <div class="feature-card">
              <div class="feature-icon">
                <svg xmlns="http://www.w3.org/2000/svg" fill="currentColor" width="30" height="30" viewBox="0 0 576 512"><path d="M288 0C422.4 0 512 35.2 512 80l0 16 0 32c17.7 0 32 14.3 32 32l0 64c0 17.7-14.3 32-32 32l0 160c0 17.7-14.3 32-32 32l0 32c0 17.7-14.3 32-32 32l-32 0c-17.7 0-32-14.3-32-32l0-32-192 0 0 32c0 17.7-14.3 32-32 32l-32 0c-17.7 0-32-14.3-32-32l0-32c-17.7 0-32-14.3-32-32l0-160c-17.7 0-32-14.3-32-32l0-64c0-17.7 14.3-32 32-32c0 0 0 0 0 0l0-32s0 0 0 0l0-16C64 35.2 153.6 0 288 0zM128 160l0 96c0 17.7 14.3 32 32 32l112 0 0-160-112 0c-17.7 0-32 14.3-32 32zM304 288l112 0c17.7 0 32-14.3 32-32l0-96c0-17.7-14.3-32-32-32l-112 0 0 160zM144 400a32 32 0 1 0 0-64 32 32 0 1 0 0 64zm288 0a32 32 0 1 0 0-64 32 32 0 1 0 0 64zM384 80c0-8.8-7.2-16-16-16L208 64c-8.8 0-16 7.2-16 16s7.2 16 16 16l160 0c8.8 0 16-7.2 16-16z"/></svg>
              </div>
              <h3 class="feature-title">Large choix de compagnies</h3>
              <p>
                Plus de 20 compagnies de bus partenaires pour une multitude de
                destinations.
              </p>
            </div>
          </div>
          <div class="col-md-4">
            <div class="feature-card">
              <div class="feature-icon">
                <svg xmlns="http://www.w3.org/2000/svg" fill="currentColor" width="30" height="30" viewBox="0 0 576 512"><path d="M0 112.5L0 422.3c0 18 10.1 35 27 41.3c87 32.5 174 10.3 261-11.9c79.8-20.3 159.6-40.7 239.3-18.9c23 6.3 48.7-9.5 48.7-33.4l0-309.9c0-18-10.1-35-27-41.3C462 15.9 375 38.1 288 60.3C208.2 80.6 128.4 100.9 48.7 79.1C25.6 72.8 0 88.6 0 112.5zM288 352c-44.2 0-80-43-80-96s35.8-96 80-96s80 43 80 96s-35.8 96-80 96zM64 352c35.3 0 64 28.7 64 64l-64 0 0-64zm64-208c0 35.3-28.7 64-64 64l0-64 64 0zM512 304l0 64-64 0c0-35.3 28.7-64 64-64zM448 96l64 0 0 64c-35.3 0-64-28.7-64-64z"/></svg>
              </div>
              <h3 class="feature-title">Paiement sécurisé</h3>
              <p>
                Paiement en ligne sécurisé ou en espèces dans nos points de
                vente partenaires.
              </p>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- Testimonials Section -->
    <section class="py-5">
      <div class="container">
        <h2 class="section-title">Ce que disent nos clients</h2>
        <div class="row">
          <div class="col-md-4">
            <div class="testimonial-card">
              <div class="d-flex align-items-center mb-3">
                <div class="testimonial-avatar">
                  <img
                    src="/img/testimonial-1.jpg"
                    alt="Client"
                    class="img-fluid rounded-circle"
                  />
                </div>
                <div>
                  <h4 class="fs-5 mb-0">Moudjinath ABOUBAKA</h4>
                  <p class="text-muted mb-0">Parakou</p>
                </div>
              </div>
              <div class="testimonial-rating">
                <svg xmlns="http://www.w3.org/2000/svg" fill="currentColor" width="16" height="16" viewBox="0 0 576 512"><path d="M316.9 18C311.6 7 300.4 0 288.1 0s-23.4 7-28.8 18L195 150.3 51.4 171.5c-12 1.8-22 10.2-25.7 21.7s-.7 24.2 7.9 32.7L137.8 329 113.2 474.7c-2 12 3 24.2 12.9 31.3s23 8 33.8 2.3l128.3-68.5 128.3 68.5c10.8 5.7 23.9 4.9 33.8-2.3s14.9-19.3 12.9-31.3L438.5 329 542.7 225.9c8.6-8.5 11.7-21.2 7.9-32.7s-13.7-19.9-25.7-21.7L381.2 150.3 316.9 18z"/></svg>
                <svg xmlns="http://www.w3.org/2000/svg" fill="currentColor" width="16" height="16" viewBox="0 0 576 512"><path d="M316.9 18C311.6 7 300.4 0 288.1 0s-23.4 7-28.8 18L195 150.3 51.4 171.5c-12 1.8-22 10.2-25.7 21.7s-.7 24.2 7.9 32.7L137.8 329 113.2 474.7c-2 12 3 24.2 12.9 31.3s23 8 33.8 2.3l128.3-68.5 128.3 68.5c10.8 5.7 23.9 4.9 33.8-2.3s14.9-19.3 12.9-31.3L438.5 329 542.7 225.9c8.6-8.5 11.7-21.2 7.9-32.7s-13.7-19.9-25.7-21.7L381.2 150.3 316.9 18z"/></svg>
                <svg xmlns="http://www.w3.org/2000/svg" fill="currentColor" width="16" height="16" viewBox="0 0 576 512"><path d="M316.9 18C311.6 7 300.4 0 288.1 0s-23.4 7-28.8 18L195 150.3 51.4 171.5c-12 1.8-22 10.2-25.7 21.7s-.7 24.2 7.9 32.7L137.8 329 113.2 474.7c-2 12 3 24.2 12.9 31.3s23 8 33.8 2.3l128.3-68.5 128.3 68.5c10.8 5.7 23.9 4.9 33.8-2.3s14.9-19.3 12.9-31.3L438.5 329 542.7 225.9c8.6-8.5 11.7-21.2 7.9-32.7s-13.7-19.9-25.7-21.7L381.2 150.3 316.9 18z"/></svg>
                <svg xmlns="http://www.w3.org/2000/svg" fill="currentColor" width="16" height="16" viewBox="0 0 576 512"><path d="M316.9 18C311.6 7 300.4 0 288.1 0s-23.4 7-28.8 18L195 150.3 51.4 171.5c-12 1.8-22 10.2-25.7 21.7s-.7 24.2 7.9 32.7L137.8 329 113.2 474.7c-2 12 3 24.2 12.9 31.3s23 8 33.8 2.3l128.3-68.5 128.3 68.5c10.8 5.7 23.9 4.9 33.8-2.3s14.9-19.3 12.9-31.3L438.5 329 542.7 225.9c8.6-8.5 11.7-21.2 7.9-32.7s-13.7-19.9-25.7-21.7L381.2 150.3 316.9 18z"/></svg>
                <svg xmlns="http://www.w3.org/2000/svg" fill="currentColor" width="16" height="16" viewBox="0 0 576 512"><path d="M316.9 18C311.6 7 300.4 0 288.1 0s-23.4 7-28.8 18L195 150.3 51.4 171.5c-12 1.8-22 10.2-25.7 21.7s-.7 24.2 7.9 32.7L137.8 329 113.2 474.7c-2 12 3 24.2 12.9 31.3s23 8 33.8 2.3l128.3-68.5 128.3 68.5c10.8 5.7 23.9 4.9 33.8-2.3s14.9-19.3 12.9-31.3L438.5 329 542.7 225.9c8.6-8.5 11.7-21.2 7.9-32.7s-13.7-19.9-25.7-21.7L381.2 150.3 316.9 18z"/></svg>
              </div>
              <p>
                "Service vraiment pratique. J'ai économisé beaucoup de temps en
                réservant mon billet pour Accra depuis mon téléphone. Je
                recommande !"
              </p>
            </div>
          </div>
          <div class="col-md-4">
            <div class="testimonial-card">
              <div class="d-flex align-items-center mb-3">
                <div class="testimonial-avatar">
                  <img
                    src="/img/testimonial-2.jpg"
                    alt="Client"
                    class="img-fluid rounded-circle"
                  />
                </div>
                <div>
                  <h4 class="fs-5 mb-0">Olivier QUENUM</h4>
                  <p class="text-muted mb-0">Cotonou</p>
                </div>
              </div>
              <div class="testimonial-rating">
                <svg xmlns="http://www.w3.org/2000/svg" fill="currentColor" width="16" height="16" viewBox="0 0 576 512"><path d="M316.9 18C311.6 7 300.4 0 288.1 0s-23.4 7-28.8 18L195 150.3 51.4 171.5c-12 1.8-22 10.2-25.7 21.7s-.7 24.2 7.9 32.7L137.8 329 113.2 474.7c-2 12 3 24.2 12.9 31.3s23 8 33.8 2.3l128.3-68.5 128.3 68.5c10.8 5.7 23.9 4.9 33.8-2.3s14.9-19.3 12.9-31.3L438.5 329 542.7 225.9c8.6-8.5 11.7-21.2 7.9-32.7s-13.7-19.9-25.7-21.7L381.2 150.3 316.9 18z"/></svg>
                <svg xmlns="http://www.w3.org/2000/svg" fill="currentColor" width="16" height="16" viewBox="0 0 576 512"><path d="M316.9 18C311.6 7 300.4 0 288.1 0s-23.4 7-28.8 18L195 150.3 51.4 171.5c-12 1.8-22 10.2-25.7 21.7s-.7 24.2 7.9 32.7L137.8 329 113.2 474.7c-2 12 3 24.2 12.9 31.3s23 8 33.8 2.3l128.3-68.5 128.3 68.5c10.8 5.7 23.9 4.9 33.8-2.3s14.9-19.3 12.9-31.3L438.5 329 542.7 225.9c8.6-8.5 11.7-21.2 7.9-32.7s-13.7-19.9-25.7-21.7L381.2 150.3 316.9 18z"/></svg>
                <svg xmlns="http://www.w3.org/2000/svg" fill="currentColor" width="16" height="16" viewBox="0 0 576 512"><path d="M316.9 18C311.6 7 300.4 0 288.1 0s-23.4 7-28.8 18L195 150.3 51.4 171.5c-12 1.8-22 10.2-25.7 21.7s-.7 24.2 7.9 32.7L137.8 329 113.2 474.7c-2 12 3 24.2 12.9 31.3s23 8 33.8 2.3l128.3-68.5 128.3 68.5c10.8 5.7 23.9 4.9 33.8-2.3s14.9-19.3 12.9-31.3L438.5 329 542.7 225.9c8.6-8.5 11.7-21.2 7.9-32.7s-13.7-19.9-25.7-21.7L381.2 150.3 316.9 18z"/></svg>
                <svg xmlns="http://www.w3.org/2000/svg" fill="currentColor" width="16" height="16" viewBox="0 0 576 512"><path d="M316.9 18C311.6 7 300.4 0 288.1 0s-23.4 7-28.8 18L195 150.3 51.4 171.5c-12 1.8-22 10.2-25.7 21.7s-.7 24.2 7.9 32.7L137.8 329 113.2 474.7c-2 12 3 24.2 12.9 31.3s23 8 33.8 2.3l128.3-68.5 128.3 68.5c10.8 5.7 23.9 4.9 33.8-2.3s14.9-19.3 12.9-31.3L438.5 329 542.7 225.9c8.6-8.5 11.7-21.2 7.9-32.7s-13.7-19.9-25.7-21.7L381.2 150.3 316.9 18z"/></svg>
                <svg xmlns="http://www.w3.org/2000/svg" fill="currentColor" width="16" height="16" viewBox="0 0 576 512"><path d="M287.9 0c9.2 0 17.6 5.2 21.6 13.5l68.6 141.3 153.2 22.6c9 1.3 16.5 7.6 19.3 16.3s.5 18.1-5.9 24.5L433.6 328.4l26.2 155.6c1.5 9-2.2 18.1-9.7 23.5s-17.3 6-25.3 1.7l-137-73.2L151 509.1c-8.1 4.3-17.9 3.7-25.3-1.7s-11.2-14.5-9.7-23.5l26.2-155.6L31.1 218.2c-6.5-6.4-8.7-15.9-5.9-24.5s10.3-14.9 19.3-16.3l153.2-22.6L266.3 13.5C270.4 5.2 278.7 0 287.9 0zm0 79L235.4 187.2c-3.5 7.1-10.2 12.1-18.1 13.3L99 217.9 184.9 303c5.5 5.5 8.1 13.3 6.8 21L171.4 443.7l105.2-56.2c7.1-3.8 15.6-3.8 22.6 0l105.2 56.2L384.2 324.1c-1.3-7.7 1.2-15.5 6.8-21l85.9-85.1L358.6 200.5c-7.8-1.2-14.6-6.1-18.1-13.3L287.9 79z"/></svg>
              </div>
              <p>
                "J'utilise régulièrement ce service pour mes voyages d'affaires.
                La possibilité de comparer les prix et les horaires est très
                appréciable."
              </p>
            </div>
          </div>
          <div class="col-md-4">
            <div class="testimonial-card">
              <div class="d-flex align-items-center mb-3">
                <div class="testimonial-avatar">
                  <img
                    src="/img/testimonial-3.jpg"
                    alt="Client"
                    class="img-fluid rounded-circle"
                  />
                </div>
                <div>
                  <h4 class="fs-5 mb-0">Jean Diallo</h4>
                  <p class="text-muted mb-0">Abidjan</p>
                </div>
              </div>
              <div class="testimonial-rating">
                <svg xmlns="http://www.w3.org/2000/svg" fill="currentColor" width="16" height="16" viewBox="0 0 576 512"><path d="M316.9 18C311.6 7 300.4 0 288.1 0s-23.4 7-28.8 18L195 150.3 51.4 171.5c-12 1.8-22 10.2-25.7 21.7s-.7 24.2 7.9 32.7L137.8 329 113.2 474.7c-2 12 3 24.2 12.9 31.3s23 8 33.8 2.3l128.3-68.5 128.3 68.5c10.8 5.7 23.9 4.9 33.8-2.3s14.9-19.3 12.9-31.3L438.5 329 542.7 225.9c8.6-8.5 11.7-21.2 7.9-32.7s-13.7-19.9-25.7-21.7L381.2 150.3 316.9 18z"/></svg>
                <svg xmlns="http://www.w3.org/2000/svg" fill="currentColor" width="16" height="16" viewBox="0 0 576 512"><path d="M316.9 18C311.6 7 300.4 0 288.1 0s-23.4 7-28.8 18L195 150.3 51.4 171.5c-12 1.8-22 10.2-25.7 21.7s-.7 24.2 7.9 32.7L137.8 329 113.2 474.7c-2 12 3 24.2 12.9 31.3s23 8 33.8 2.3l128.3-68.5 128.3 68.5c10.8 5.7 23.9 4.9 33.8-2.3s14.9-19.3 12.9-31.3L438.5 329 542.7 225.9c8.6-8.5 11.7-21.2 7.9-32.7s-13.7-19.9-25.7-21.7L381.2 150.3 316.9 18z"/></svg>
                <svg xmlns="http://www.w3.org/2000/svg" fill="currentColor" width="16" height="16" viewBox="0 0 576 512"><path d="M316.9 18C311.6 7 300.4 0 288.1 0s-23.4 7-28.8 18L195 150.3 51.4 171.5c-12 1.8-22 10.2-25.7 21.7s-.7 24.2 7.9 32.7L137.8 329 113.2 474.7c-2 12 3 24.2 12.9 31.3s23 8 33.8 2.3l128.3-68.5 128.3 68.5c10.8 5.7 23.9 4.9 33.8-2.3s14.9-19.3 12.9-31.3L438.5 329 542.7 225.9c8.6-8.5 11.7-21.2 7.9-32.7s-13.7-19.9-25.7-21.7L381.2 150.3 316.9 18z"/></svg>
                <svg xmlns="http://www.w3.org/2000/svg" fill="currentColor" width="16" height="16" viewBox="0 0 576 512"><path d="M316.9 18C311.6 7 300.4 0 288.1 0s-23.4 7-28.8 18L195 150.3 51.4 171.5c-12 1.8-22 10.2-25.7 21.7s-.7 24.2 7.9 32.7L137.8 329 113.2 474.7c-2 12 3 24.2 12.9 31.3s23 8 33.8 2.3l128.3-68.5 128.3 68.5c10.8 5.7 23.9 4.9 33.8-2.3s14.9-19.3 12.9-31.3L438.5 329 542.7 225.9c8.6-8.5 11.7-21.2 7.9-32.7s-13.7-19.9-25.7-21.7L381.2 150.3 316.9 18z"/></svg>
                <svg xmlns="http://www.w3.org/2000/svg" fill="currentColor" width="16" height="16" viewBox="0 0 576 512"><path d="M288 376.4l.1-.1 26.4 14.1 85.2 45.5-16.5-97.6-4.8-28.7 20.7-20.5 70.1-69.3-96.1-14.2-29.3-4.3-12.9-26.6L288.1 86.9l-.1 .3 0 289.2zm175.1 98.3c2 12-3 24.2-12.9 31.3s-23 8-33.8 2.3L288.1 439.8 159.8 508.3C149 514 135.9 513.1 126 506s-14.9-19.3-12.9-31.3L137.8 329 33.6 225.9c-8.6-8.5-11.7-21.2-7.9-32.7s13.7-19.9 25.7-21.7L195 150.3 259.4 18c5.4-11 16.5-18 28.8-18s23.4 7 28.8 18l64.3 132.3 143.6 21.2c12 1.8 22 10.2 25.7 21.7s.7 24.2-7.9 32.7L438.5 329l24.6 145.7z"/></svg>
              </div>
              <p>
                "Excellente application qui simplifie vraiment les voyages en
                bus. Les notifications et le suivi des réservations sont des
                fonctionnalités très utiles."
              </p>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- Footer -->
    <footer>
      <div class="container">
        <div class="row">
          <div class="col-lg-3 col-md-6 mb-4 mb-lg-0">
            <h3 class="footer-title">EasyBus</h3>
            <p>
              La plateforme de réservation de billets de bus en ligne leader en
              Afrique de l'Ouest.
            </p>
            <div class="social-links">
              <a href="#">
                <svg xmlns="http://www.w3.org/2000/svg" fill="currentColor" width="16" height="16" viewBox="0 0 320 512"><path d="M80 299.3V512H196V299.3h86.5l18-97.8H196V166.9c0-51.7 20.3-71.5 72.7-71.5c16.3 0 29.4 .4 37 1.2V7.9C291.4 4 256.4 0 236.2 0C129.3 0 80 50.5 80 159.4v42.1H14v97.8H80z"/></svg>
              </a>
              <a href="#">
                <svg xmlns="http://www.w3.org/2000/svg" fill="currentColor" width="16" height="16" viewBox="0 0 512 512"><path d="M389.2 48h70.6L305.6 224.2 487 464H345L233.7 318.6 106.5 464H35.8L200.7 275.5 26.8 48H172.4L272.9 180.9 389.2 48zM364.4 421.8h39.1L151.1 88h-42L364.4 421.8z"/></svg>
              </a>
              <a href="#">
                <svg xmlns="http://www.w3.org/2000/svg" fill="currentColor" width="16" height="16" viewBox="0 0 448 512"><path d="M224.1 141c-63.6 0-114.9 51.3-114.9 114.9s51.3 114.9 114.9 114.9S339 319.5 339 255.9 287.7 141 224.1 141zm0 189.6c-41.1 0-74.7-33.5-74.7-74.7s33.5-74.7 74.7-74.7 74.7 33.5 74.7 74.7-33.6 74.7-74.7 74.7zm146.4-194.3c0 14.9-12 26.8-26.8 26.8-14.9 0-26.8-12-26.8-26.8s12-26.8 26.8-26.8 26.8 12 26.8 26.8zm76.1 27.2c-1.7-35.9-9.9-67.7-36.2-93.9-26.2-26.2-58-34.4-93.9-36.2-37-2.1-147.9-2.1-184.9 0-35.8 1.7-67.6 9.9-93.9 36.1s-34.4 58-36.2 93.9c-2.1 37-2.1 147.9 0 184.9 1.7 35.9 9.9 67.7 36.2 93.9s58 34.4 93.9 36.2c37 2.1 147.9 2.1 184.9 0 35.9-1.7 67.7-9.9 93.9-36.2 26.2-26.2 34.4-58 36.2-93.9 2.1-37 2.1-147.8 0-184.8zM398.8 388c-7.8 19.6-22.9 34.7-42.6 42.6-29.5 11.7-99.5 9-132.1 9s-102.7 2.6-132.1-9c-19.6-7.8-34.7-22.9-42.6-42.6-11.7-29.5-9-99.5-9-132.1s-2.6-102.7 9-132.1c7.8-19.6 22.9-34.7 42.6-42.6 29.5-11.7 99.5-9 132.1-9s102.7-2.6 132.1 9c19.6 7.8 34.7 22.9 42.6 42.6 11.7 29.5 9 99.5 9 132.1s2.7 102.7-9 132.1z"/></svg>
              </a>
              <a href="#">
                <svg xmlns="http://www.w3.org/2000/svg" fill="currentColor" width="16" height="16" viewBox="0 0 448 512"><path d="M100.3 448H7.4V148.9h92.9zM53.8 108.1C24.1 108.1 0 83.5 0 53.8a53.8 53.8 0 0 1 107.6 0c0 29.7-24.1 54.3-53.8 54.3zM447.9 448h-92.7V302.4c0-34.7-.7-79.2-48.3-79.2-48.3 0-55.7 37.7-55.7 76.7V448h-92.8V148.9h89.1v40.8h1.3c12.4-23.5 42.7-48.3 87.9-48.3 94 0 111.3 61.9 111.3 142.3V448z"/></svg>
              </a>
            </div>
          </div>
          <div class="col-lg-3 col-md-6 mb-4 mb-lg-0">
            <h4 class="footer-title">Liens rapides</h4>
            <ul class="footer-links">
              <li><a href="#">Accueil</a></li>
              <li><a href="#">À propos</a></li>
              <li><a href="#">Compagnies partenaires</a></li>
              <li><a href="#">Destinations</a></li>
              <li><a href="#">Contact</a></li>
            </ul>
          </div>
          <div class="col-lg-3 col-md-6 mb-4 mb-lg-0">
            <h4 class="footer-title">Assistance</h4>
            <ul class="footer-links">
              <li><a href="#">Centre d'aide</a></li>
              <li><a href="#">FAQ</a></li>
              <li><a href="#">Comment réserver</a></li>
              <li><a href="#">Politique d'annulation</a></li>
              <li><a href="#">Réclamations</a></li>
            </ul>
          </div>
          <div class="col-lg-3 col-md-6">
            <h4 class="footer-title">Contact</h4>
            <ul class="footer-links">
              <li>
                <svg class="me-2" xmlns="http://www.w3.org/2000/svg" fill="currentColor" width="16" height="16" viewBox="0 0 384 512"><path d="M215.7 499.2C267 435 384 279.4 384 192C384 86 298 0 192 0S0 86 0 192c0 87.4 117 243 168.3 307.2c12.3 15.3 35.1 15.3 47.4 0zM192 128a64 64 0 1 1 0 128 64 64 0 1 1 0-128z"/></svg>
                Rue 123, Cotonou, Bénin
              </li>
              <li>
                <svg class="me-2" xmlns="http://www.w3.org/2000/svg" fill="currentColor" width="16" height="16" viewBox="0 0 512 512"><path d="M164.9 24.6c-7.7-18.6-28-28.5-47.4-23.2l-88 24C12.1 30.2 0 46 0 64C0 311.4 200.6 512 448 512c18 0 33.8-12.1 38.6-29.5l24-88c5.3-19.4-4.6-39.7-23.2-47.4l-96-40c-16.3-6.8-35.2-2.1-46.3 11.6L304.7 368C234.3 334.7 177.3 277.7 144 207.3L193.3 167c13.7-11.2 18.4-30 11.6-46.3l-40-96z"/></svg>
                +229 01 21 00 00 00
              </li>
              <li>
                <svg class="me-2" xmlns="http://www.w3.org/2000/svg" fill="currentColor" width="16" height="16" viewBox="0 0 512 512"><path d="M48 64C21.5 64 0 85.5 0 112c0 15.1 7.1 29.3 19.2 38.4L236.8 313.6c11.4 8.5 27 8.5 38.4 0L492.8 150.4c12.1-9.1 19.2-23.3 19.2-38.4c0-26.5-21.5-48-48-48L48 64zM0 176L0 384c0 35.3 28.7 64 64 64l384 0c35.3 0 64-28.7 64-64l0-208L294.4 339.2c-22.8 17.1-54 17.1-76.8 0L0 176z"/></svg>
                <EMAIL>
              </li>
            </ul>
            <div class="mt-3">
              <a href="#" class="btn btn-outline-light btn-sm"
                >Devenir partenaire</a
              >
            </div>
          </div>
        </div>
        <div class="footer-bottom text-center">
          <p class="mb-0">&copy; 2025 EasyBus. Tous droits réservés.</p>
        </div>
      </div>
    </footer>

    <script src="/js/bootstrap.bundle.min.js"></script>

    <!-- Application JavaScript -->
    <script>
      let cities = [];

      async function fetchCities() {
        try {
          const response = await fetch(
            "https://bus-booking.test/api/v1/locations/"
          );
          if (!response.ok) {
            throw new Error("Erreur lors du chargement des villes");
          }
          const data = await response.json();
          cities = data;
        } catch (error) {
          console.error("Erreur:", error);
        }
      }

      function generateCityDropdown(citiesList, excludeId = null) {
        return citiesList
          .filter((city) => city.location_id !== excludeId)
          .map(
            (city) => `
                <div class="city-item" data-id="${city.location_id}">
                    <div class="city-name">${city.location_name}</div>
                    <div class="city-details">${city.region}, ${city.country}</div>
                </div>
            `
          )
          .join("");
      }

      function showDropdown(inputId, dropdownId, excludeId) {
        const dropdown = document.getElementById(dropdownId);
        dropdown.innerHTML = generateCityDropdown(cities, excludeId);
        dropdown.classList.add("show");
      }

      function hideDropdowns() {
        document.querySelectorAll(".city-dropdown").forEach((dropdown) => {
          dropdown.classList.remove("show");
        });
      }

      ["departure", "destination"].forEach((field) => {
        const input = document.getElementById(field);
        const dropdown = document.getElementById(`${field}-dropdown`);
        const hiddenInput = document.getElementById(`${field}-id`);

        input.addEventListener("focus", () => {
          const otherField =
            field === "departure" ? "destination" : "departure";
          const excludeId = document.getElementById(`${otherField}-id`).value;
          showDropdown(
            field,
            `${field}-dropdown`,
            excludeId ? parseInt(excludeId) : null
          );
        });

        dropdown.addEventListener("click", (e) => {
          const cityItem = e.target.closest(".city-item");
          if (cityItem) {
            const id = cityItem.dataset.id;
            const city = cities.find((c) => c.location_id == id);
            input.value = city.location_name;
            hiddenInput.value = id;
            hideDropdowns();
          }
        });
      });

      // Ajouter des écouteurs pour gérer l'effacement des champs
      const departureInput = document.getElementById("departure");
      const departureIdInput = document.getElementById("departure-id");
      const destinationInput = document.getElementById("destination");
      const destinationIdInput = document.getElementById("destination-id");

      departureInput.addEventListener("input", () => {
        if (departureInput.value.trim() === "") {
          departureIdInput.value = "";
        }
      });

      destinationInput.addEventListener("input", () => {
        if (destinationInput.value.trim() === "") {
          destinationIdInput.value = "";
        }
      });

      document.addEventListener("mousedown", (e) => {
        const departureInput = document.getElementById("departure");
        const departureDropdown = document.getElementById("departure-dropdown");
        const destinationInput = document.getElementById("destination");
        const destinationDropdown = document.getElementById(
          "destination-dropdown"
        );

        if (
          !departureInput.contains(e.target) &&
          !departureDropdown.contains(e.target) &&
          !destinationInput.contains(e.target) &&
          !destinationDropdown.contains(e.target)
        ) {
          hideDropdowns();
        }
      });

      const today = new Date().toISOString().split("T")[0];
      document.getElementById("departure-date").value = today;
      document.getElementById("departure-date").min = today;

      window.onload = fetchCities;
    </script>
  </body>
</html>
