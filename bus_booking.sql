
SET SQL_MODE = "NO_AUTO_VALUE_ON_ZERO";
START TRANSACTION;
SET time_zone = "+00:00";

CREATE TABLE `amenity` (
  `amenity_id` int NOT NULL,
  `amenity_name` varchar(255) NOT NULL,
  `description` text,
  `created_by` int DEFAULT NULL,
  `updated_by` int DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3;

--
-- Déchargement des données de la table `amenity`
--

INSERT INTO `amenity` (`amenity_id`, `amenity_name`, `description`, `created_by`, `updated_by`) VALUES
(1, 'Wi-Fi', 'Accès internet sans fil', NULL, NULL),
(2, 'Climatisation', 'Système de climatisation', NULL, NULL),
(3, 'Toilettes', 'Toilettes à bord', NULL, NULL),
(4, '<PERSON><PERSON>s inclinables', 'Sièges avec fonction inclinable', NULL, NULL),
(5, 'Prises électriques', 'Prises pour charger les appareils', NULL, NULL);

-- --------------------------------------------------------

--
-- Structure de la table `booked_seat`
--

CREATE TABLE `booked_seat` (
  `booked_seat_id` int NOT NULL,
  `seat_id` int NOT NULL,
  `trip_id` int NOT NULL,
  `booking_id` int NOT NULL,
  `status` enum('locked','occupied') DEFAULT 'locked',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `created_by` int DEFAULT NULL,
  `updated_by` int DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3;

--
-- Déchargement des données de la table `booked_seat`
--

INSERT INTO `booked_seat` (`booked_seat_id`, `seat_id`, `trip_id`, `booking_id`, `status`, `created_at`, `updated_at`, `created_by`, `updated_by`) VALUES
(1, 1, 6, 1, 'occupied', '2025-03-02 23:03:35', '2025-03-11 02:01:55', NULL, NULL),
(2, 2, 1, 1, 'occupied', '2025-03-02 23:03:35', '2025-03-02 23:03:35', NULL, NULL),
(3, 3, 2, 2, 'locked', '2025-03-02 23:03:35', '2025-03-02 23:03:35', NULL, NULL),
(4, 4, 3, 3, 'occupied', '2025-03-02 23:03:35', '2025-03-02 23:03:35', NULL, NULL),
(6, 1, 8, 6, 'locked', '2025-03-03 20:17:38', '2025-03-11 02:02:19', 6, NULL),
(7, 2, 2, 6, 'locked', '2025-03-03 20:17:38', '2025-03-03 20:17:38', 6, NULL),
(8, 1, 1, 6, 'occupied', '2025-05-15 02:51:41', '2025-05-15 02:51:41', NULL, NULL),
(9, 3, 1, 2, 'occupied', '2025-05-15 02:51:41', '2025-05-15 02:51:41', NULL, NULL),
(10, 6, 1, 5, 'occupied', '2025-05-15 02:51:41', '2025-05-15 02:51:41', NULL, NULL),
(11, 9, 1, 4, 'occupied', '2025-05-15 02:51:41', '2025-05-15 02:51:41', NULL, NULL),
(12, 17, 1, 3, 'occupied', '2025-05-15 02:51:41', '2025-05-15 02:51:41', NULL, NULL),
(13, 24, 1, 1, 'occupied', '2025-05-15 02:51:41', '2025-05-15 02:51:41', NULL, NULL),
(14, 35, 1, 2, 'occupied', '2025-05-15 02:51:41', '2025-05-15 02:51:41', NULL, NULL),
(15, 43, 1, 6, 'occupied', '2025-05-15 02:51:41', '2025-05-15 02:51:41', NULL, NULL);

-- --------------------------------------------------------

--
-- Structure de la table `booking`
--

CREATE TABLE `booking` (
  `booking_id` int NOT NULL,
  `user_id` int NOT NULL,
  `trip_id` int NOT NULL,
  `boarding_stop_id` int NOT NULL,
  `dropping_stop_id` int NOT NULL,
  `total_amount` decimal(10,2) NOT NULL,
  `booking_notes` text,
  `booking_status` enum('confirmed','cancelled','pending') DEFAULT 'pending',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `created_by` int DEFAULT NULL,
  `cancelled_by` int DEFAULT NULL,
  `updated_by` int DEFAULT NULL
) ;

--
-- Déchargement des données de la table `booking`
--

INSERT INTO `booking` (`booking_id`, `user_id`, `trip_id`, `boarding_stop_id`, `dropping_stop_id`, `total_amount`, `booking_notes`, `booking_status`, `created_at`, `updated_at`, `created_by`, `cancelled_by`, `updated_by`) VALUES
(1, 1, 1, 1, 2, 5000.00, 'Réservation pour Jean Koffi', 'confirmed', '2025-03-02 23:03:35', '2025-03-02 23:03:35', NULL, NULL, NULL),
(2, 5, 2, 2, 3, 15000.00, 'Réservation pour Moussa Diop', 'pending', '2025-03-02 23:03:35', '2025-03-02 23:03:35', NULL, NULL, NULL),
(3, 1, 3, 3, 4, 12000.00, 'Autre réservation pour Jean Koffi', 'cancelled', '2025-03-02 23:03:35', '2025-03-02 23:03:35', NULL, 4, NULL),
(4, 6, 1, 1, 2, 5000.00, NULL, 'cancelled', '2025-03-03 19:44:39', '2025-03-03 23:03:45', 6, 6, NULL),
(5, 6, 1, 1, 2, 50.00, NULL, 'pending', '2025-03-03 20:11:02', '2025-03-03 20:11:02', 6, NULL, NULL),
(6, 6, 2, 1, 2, 50.00, NULL, 'pending', '2025-03-03 20:17:38', '2025-03-03 20:17:38', 6, NULL, NULL);

-- --------------------------------------------------------

--
-- Structure de la table `bus`
--

CREATE TABLE `bus` (
  `bus_id` int NOT NULL,
  `registration_number` varchar(50) NOT NULL,
  `brand` varchar(255) NOT NULL,
  `model` varchar(255) NOT NULL,
  `capacity` int NOT NULL,
  `bus_photos` json DEFAULT NULL,
  `bus_type` enum('standard','vip') DEFAULT 'standard',
  `seat_plan_id` int DEFAULT NULL,
  `year_manufactured` year DEFAULT NULL,
  `status` enum('active','inactive','under_maintenance') DEFAULT 'active',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `created_by` int DEFAULT NULL,
  `updated_by` int DEFAULT NULL
) ;

--
-- Déchargement des données de la table `bus`
--

INSERT INTO `bus` (`bus_id`, `registration_number`, `brand`, `model`, `capacity`, `bus_photos`, `bus_type`, `seat_plan_id`, `year_manufactured`, `status`, `created_at`, `updated_at`, `created_by`, `updated_by`) VALUES
(1, 'BJ-1234-AB', 'Toyota', 'Coaster', 30, '[\"http://example.com/bus1.jpg\"]', 'standard', 1, '2015', 'active', '2025-03-02 23:03:34', '2025-03-02 23:03:34', NULL, NULL),
(2, 'TG-5678-CD', 'Mercedes', 'Sprinter', 20, '[\"http://example.com/bus2.jpg\"]', 'vip', 2, '2018', 'active', '2025-03-02 23:03:34', '2025-03-02 23:03:34', NULL, NULL),
(3, 'BF-9012-EF', 'Iveco', 'Daily', 25, '[\"http://example.com/bus3.jpg\"]', 'standard', 3, '2016', 'under_maintenance', '2025-03-02 23:03:34', '2025-03-02 23:03:34', NULL, NULL),
(4, 'NE-3456-GH', 'MAN', 'Lion', 40, '[\"http://example.com/bus4.jpg\"]', 'vip', 4, '2019', 'active', '2025-03-02 23:03:34', '2025-03-02 23:03:34', NULL, NULL),
(5, 'SN-7890-IJ', 'Volvo', 'B7R', 50, '[\"http://example.com/bus5.jpg\"]', 'standard', 5, '2017', 'inactive', '2025-03-02 23:03:34', '2025-03-02 23:03:34', NULL, NULL);

-- --------------------------------------------------------

--
-- Structure de la table `bus_amenity`
--

CREATE TABLE `bus_amenity` (
  `bus_amenity_id` int NOT NULL,
  `bus_id` int NOT NULL,
  `amenity_id` int NOT NULL,
  `created_by` int DEFAULT NULL,
  `updated_by` int DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3;

--
-- Déchargement des données de la table `bus_amenity`
--

INSERT INTO `bus_amenity` (`bus_amenity_id`, `bus_id`, `amenity_id`, `created_by`, `updated_by`) VALUES
(1, 1, 1, NULL, NULL),
(2, 1, 2, NULL, NULL),
(3, 2, 1, NULL, NULL),
(4, 2, 2, NULL, NULL),
(5, 2, 3, NULL, NULL);

-- --------------------------------------------------------

--
-- Structure de la table `location`
--

CREATE TABLE `location` (
  `location_id` int NOT NULL,
  `location_name` varchar(255) NOT NULL,
  `region` varchar(255) NOT NULL,
  `country` varchar(255) NOT NULL,
  `time_zone` varchar(255) NOT NULL,
  `coordinates` point NOT NULL
) ;

--
-- Déchargement des données de la table `location`
--

INSERT INTO `location` (`location_id`, `location_name`, `region`, `country`, `time_zone`, `coordinates`, `status`, `created_at`, `updated_at`, `created_by`, `updated_by`) VALUES
(1, 'Cotonou', 'Littoral', 'Bénin', 'Africa/Porto-Novo', 0xe61000000101000000c58f31772d210340d0d556ec2f7b1940, 'active', '2025-03-02 23:03:34', '2025-03-02 23:03:34', NULL, NULL),
(2, 'Parakou', 'Borgou', 'Bénin', 'Africa/Porto-Novo', 0xe61000000101000000840d4faf9465f33fcdcccccccc8c1840, 'active', '2025-03-02 23:03:34', '2025-05-15 00:11:58', NULL, NULL),
(3, 'Natitingou', 'Atacora', 'Bénin', 'Africa/Porto-Novo', 0xe61000000101000000ffb27bf2b050f8bf03780b2428be2840, 'active', '2025-03-02 23:03:34', '2025-05-15 00:12:03', NULL, NULL),
(4, 'Bohicon', 'Zou', 'Bénin', 'Africa/Porto-Novo', 0xe610000001010000006666666666e60040f5dbd78173062b40, 'active', '2025-03-02 23:03:34', '2025-05-15 01:48:48', NULL, NULL),
(5, 'Abidjan', 'Abidjan', 'Côte d\'Ivoire', 'Africa/Abidjan', 0xe61000000101000000d656ec2fbb7731c01e166a4df36e2d40, 'active', '2025-03-02 23:03:34', '2025-05-15 01:50:41', NULL, NULL);

-- --------------------------------------------------------

--
-- Structure de la table `payment`
--

CREATE TABLE `payment` (
  `payment_id` int NOT NULL,
  `booking_id` int NOT NULL,
  `amount` decimal(10,2) NOT NULL,
  `currency` varchar(10) NOT NULL,
  `payment_method` varchar(255) NOT NULL,
  `payment_provider` varchar(255) DEFAULT NULL,
  `payment_reference` varchar(255) DEFAULT NULL,
  `payment_status` enum('successful','failed','pending','refunded') DEFAULT 'pending',
  `payment_date` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `created_by` int DEFAULT NULL
) ;

--
-- Déchargement des données de la table `payment`
--

INSERT INTO `payment` (`payment_id`, `booking_id`, `amount`, `currency`, `payment_method`, `payment_provider`, `payment_reference`, `payment_status`, `payment_date`, `created_by`) VALUES
(1, 1, 5000.00, 'XOF', 'mobile_money', 'MTN', 'REF001', 'successful', '2025-03-02 23:03:35', NULL),
(2, 2, 15000.00, 'XOF', 'credit_card', 'Visa', 'REF002', 'pending', '2025-03-02 23:03:35', NULL),
(3, 3, 12000.00, 'XOF', 'cash', NULL, 'REF003', 'successful', '2025-03-02 23:03:35', NULL);

-- --------------------------------------------------------

--
-- Structure de la table `pricing`
--

CREATE TABLE `pricing` (
  `pricing_id` int NOT NULL,
  `route_id` int DEFAULT NULL,
  `bus_type` enum('standard','vip') DEFAULT NULL,
  `seat_type` enum('standard','premium') DEFAULT NULL,
  `price` decimal(10,2) NOT NULL,
  `start_date` date DEFAULT NULL,
  `end_date` date DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `created_by` int DEFAULT NULL,
  `updated_by` int DEFAULT NULL
) ;

--
-- Déchargement des données de la table `pricing`
--

INSERT INTO `pricing` (`pricing_id`, `route_id`, `bus_type`, `seat_type`, `price`, `start_date`, `end_date`, `created_at`, `updated_at`, `created_by`, `updated_by`) VALUES
(1, 1, 'standard', 'standard', 5000.00, '2023-01-01', '2026-12-31', '2025-03-02 23:03:34', '2025-03-16 00:24:03', NULL, NULL),
(2, 1, 'vip', 'premium', 10000.00, '2023-01-01', '2026-12-31', '2025-03-02 23:03:34', '2025-03-16 00:24:10', NULL, NULL),
(3, 2, 'standard', 'standard', 15000.00, '2023-01-01', '2026-12-31', '2025-03-02 23:03:34', '2025-03-16 00:24:18', NULL, NULL),
(4, 2, 'vip', 'premium', 25000.00, '2023-01-01', '2026-12-31', '2025-03-02 23:03:34', '2025-03-16 00:24:23', NULL, NULL),
(5, 3, 'standard', 'standard', 12000.00, '2023-01-01', '2026-12-31', '2025-03-02 23:03:34', '2025-03-16 00:24:29', NULL, NULL);

-- --------------------------------------------------------

--
-- Structure de la table `route`
--

CREATE TABLE `route` (
  `route_id` int NOT NULL,
  `route_name` varchar(255) NOT NULL,
  `description` text,
  `departure_location_id` int NOT NULL,
  `destination_location_id` int NOT NULL,
  `distance` decimal(10,2) DEFAULT NULL,
  `duration` time DEFAULT NULL,
  `status` enum('active','inactive') DEFAULT 'active',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `created_by` int DEFAULT NULL,
  `updated_by` int DEFAULT NULL
) ;

--
-- Déchargement des données de la table `route`
--

INSERT INTO `route` (`route_id`, `route_name`, `description`, `departure_location_id`, `destination_location_id`, `distance`, `duration`, `status`, `created_at`, `updated_at`, `created_by`, `updated_by`) VALUES
(1, 'Cotonou - Parakou', 'Trajet entre Cotonou et Lomé', 1, 2, 418.00, '07:30:00', 'active', '2025-03-02 23:03:34', '2025-05-15 01:56:12', NULL, NULL),
(2, 'Lomé - Natitingou', 'Trajet entre Lomé et Ouagadougou', 2, 3, 500.00, '08:00:00', 'active', '2025-03-02 23:03:34', '2025-05-15 01:32:27', NULL, NULL),
(3, 'Natitingou - Niamey', 'Trajet entre Ouagadougou et Niamey', 3, 4, 400.00, '07:00:00', 'active', '2025-03-02 23:03:34', '2025-05-15 01:33:00', NULL, NULL),
(4, 'Niamey - Dakar', 'Trajet entre Niamey et Dakar', 4, 5, 1200.00, '24:00:00', 'active', '2025-03-02 23:03:34', '2025-03-02 23:03:34', NULL, NULL),
(5, 'Cotonou - Abidjan', 'Trajet direct entre Cotonou et Dakar', 1, 5, 1800.00, '36:00:00', 'inactive', '2025-03-02 23:03:34', '2025-05-15 01:33:29', NULL, NULL);

-- --------------------------------------------------------

--
-- Structure de la table `route_stop`
--

CREATE TABLE `route_stop` (
  `route_stop_id` int NOT NULL,
  `stop_id` int NOT NULL,
  `route_id` int NOT NULL,
  `stop_order` int NOT NULL,
  `stop_type` enum('boarding','intermediate','dropping') NOT NULL,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `created_by` int DEFAULT NULL,
  `updated_by` int DEFAULT NULL
) ;

--
-- Déchargement des données de la table `route_stop`
--

INSERT INTO `route_stop` (`route_stop_id`, `stop_id`, `route_id`, `stop_order`, `stop_type`, `created_at`, `updated_at`, `created_by`, `updated_by`) VALUES
(1, 1, 1, 1, 'boarding', '2025-03-02 23:03:34', '2025-03-02 23:03:34', NULL, NULL),
(2, 2, 1, 5, 'dropping', '2025-03-02 23:03:34', '2025-03-19 12:50:45', NULL, NULL),
(3, 2, 2, 1, 'boarding', '2025-03-02 23:03:34', '2025-03-02 23:03:34', NULL, NULL),
(4, 3, 2, 2, 'dropping', '2025-03-02 23:03:34', '2025-03-02 23:03:34', NULL, NULL),
(5, 3, 3, 1, 'boarding', '2025-03-02 23:03:34', '2025-03-02 23:03:34', NULL, NULL),
(6, 4, 3, 2, 'dropping', '2025-03-02 23:03:34', '2025-03-02 23:03:34', NULL, NULL),
(7, 4, 4, 1, 'boarding', '2025-03-02 23:03:34', '2025-03-02 23:03:34', NULL, NULL),
(8, 5, 4, 2, 'dropping', '2025-03-02 23:03:34', '2025-03-02 23:03:34', NULL, NULL),
(9, 1, 5, 1, 'boarding', '2025-03-02 23:03:34', '2025-03-02 23:03:34', NULL, NULL),
(10, 5, 5, 2, 'dropping', '2025-03-02 23:03:34', '2025-03-02 23:03:34', NULL, NULL),
(16, 4, 1, 3, 'intermediate', '2025-03-19 12:50:12', '2025-03-19 12:50:12', NULL, NULL);

-- --------------------------------------------------------

--
-- Structure de la table `seat`
--

CREATE TABLE `seat` (
  `seat_id` int NOT NULL,
  `bus_id` int NOT NULL,
  `seat_number` varchar(10) NOT NULL,
  `seat_type` enum('standard','premium') DEFAULT 'standard',
  `status` enum('active','inactive') DEFAULT 'active',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `created_by` int DEFAULT NULL,
  `updated_by` int DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3;

--
-- Déchargement des données de la table `seat`
--

INSERT INTO `seat` (`seat_id`, `bus_id`, `seat_number`, `seat_type`, `status`, `created_at`, `updated_at`, `created_by`, `updated_by`) VALUES
(1, 1, 'A1', 'standard', 'active', '2025-03-02 23:03:34', '2025-03-23 13:38:08', NULL, NULL),
(2, 1, 'B1', 'standard', 'active', '2025-03-02 23:03:34', '2025-03-23 13:38:36', NULL, NULL),
(3, 1, 'A2', 'standard', 'active', '2025-03-02 23:03:34', '2025-03-23 13:38:21', NULL, NULL),
(4, 1, 'B2', 'standard', 'active', '2025-03-02 23:03:34', '2025-03-23 13:38:44', NULL, NULL),
(5, 1, 'D1', 'standard', 'active', '2025-05-15 02:30:50', '2025-05-15 02:30:50', NULL, NULL),
(6, 1, 'E1', 'standard', 'active', '2025-05-15 02:30:50', '2025-05-15 02:30:50', NULL, NULL),
(7, 1, 'D2', 'standard', 'active', '2025-05-15 02:30:50', '2025-05-15 02:30:50', NULL, NULL),
(8, 1, 'E2', 'standard', 'active', '2025-05-15 02:30:50', '2025-05-15 02:30:50', NULL, NULL),
(9, 1, 'A3', 'standard', 'active', '2025-05-15 02:30:50', '2025-05-15 02:30:50', NULL, NULL),
(10, 1, 'B3', 'standard', 'active', '2025-05-15 02:30:50', '2025-05-15 02:30:50', NULL, NULL),
(11, 1, 'D3', 'standard', 'active', '2025-05-15 02:30:50', '2025-05-15 02:30:50', NULL, NULL),
(12, 1, 'E3', 'standard', 'active', '2025-05-15 02:30:50', '2025-05-15 02:30:50', NULL, NULL),
(13, 1, 'A4', 'standard', 'active', '2025-05-15 02:30:50', '2025-05-15 02:30:50', NULL, NULL),
(14, 1, 'B4', 'standard', 'active', '2025-05-15 02:30:50', '2025-05-15 02:30:50', NULL, NULL),
(15, 1, 'D4', 'standard', 'active', '2025-05-15 02:30:50', '2025-05-15 02:30:50', NULL, NULL),
(16, 1, 'E4', 'standard', 'active', '2025-05-15 02:30:50', '2025-05-15 02:30:50', NULL, NULL),
(17, 1, 'A5', 'standard', 'active', '2025-05-15 02:30:50', '2025-05-15 02:30:50', NULL, NULL),
(18, 1, 'B5', 'standard', 'active', '2025-05-15 02:30:50', '2025-05-15 02:30:50', NULL, NULL),
(21, 1, 'A6', 'standard', 'active', '2025-05-15 02:30:50', '2025-05-15 02:30:50', NULL, NULL),
(22, 1, 'B6', 'standard', 'active', '2025-05-15 02:30:50', '2025-05-15 02:30:50', NULL, NULL),
(23, 1, 'D6', 'standard', 'active', '2025-05-15 02:30:50', '2025-05-15 02:30:50', NULL, NULL),
(24, 1, 'E6', 'standard', 'active', '2025-05-15 02:30:50', '2025-05-15 02:30:50', NULL, NULL),
(25, 1, 'A7', 'standard', 'active', '2025-05-15 02:30:50', '2025-05-15 02:30:50', NULL, NULL),
(26, 1, 'B7', 'standard', 'active', '2025-05-15 02:30:50', '2025-05-15 02:30:50', NULL, NULL),
(27, 1, 'D7', 'standard', 'active', '2025-05-15 02:30:50', '2025-05-15 02:30:50', NULL, NULL),
(28, 1, 'E7', 'standard', 'active', '2025-05-15 02:30:50', '2025-05-15 02:30:50', NULL, NULL),
(29, 1, 'A8', 'standard', 'active', '2025-05-15 02:30:50', '2025-05-15 02:30:50', NULL, NULL),
(30, 1, 'B8', 'standard', 'active', '2025-05-15 02:30:50', '2025-05-15 02:30:50', NULL, NULL),
(31, 1, 'D8', 'standard', 'active', '2025-05-15 02:30:50', '2025-05-15 02:30:50', NULL, NULL),
(32, 1, 'E8', 'standard', 'active', '2025-05-15 02:30:50', '2025-05-15 02:30:50', NULL, NULL),
(33, 1, 'A9', 'standard', 'active', '2025-05-15 02:30:50', '2025-05-15 02:30:50', NULL, NULL),
(34, 1, 'B9', 'standard', 'active', '2025-05-15 02:30:50', '2025-05-15 02:30:50', NULL, NULL),
(35, 1, 'D9', 'standard', 'active', '2025-05-15 02:30:50', '2025-05-15 02:30:50', NULL, NULL),
(36, 1, 'E9', 'standard', 'active', '2025-05-15 02:30:50', '2025-05-15 02:30:50', NULL, NULL),
(37, 1, 'A10', 'standard', 'active', '2025-05-15 02:30:50', '2025-05-15 02:30:50', NULL, NULL),
(38, 1, 'B10', 'standard', 'active', '2025-05-15 02:30:50', '2025-05-15 02:30:50', NULL, NULL),
(39, 1, 'D10', 'standard', 'active', '2025-05-15 02:30:50', '2025-05-15 02:30:50', NULL, NULL),
(40, 1, 'E10', 'standard', 'active', '2025-05-15 02:30:50', '2025-05-15 02:30:50', NULL, NULL),
(41, 1, 'A11', 'standard', 'active', '2025-05-15 02:30:50', '2025-05-15 02:30:50', NULL, NULL),
(42, 1, 'B11', 'standard', 'active', '2025-05-15 02:30:50', '2025-05-15 02:30:50', NULL, NULL),
(43, 1, 'C11', 'standard', 'active', '2025-05-15 02:30:50', '2025-05-15 02:30:50', NULL, NULL),
(44, 1, 'D11', 'standard', 'active', '2025-05-15 02:30:50', '2025-05-15 02:30:50', NULL, NULL),
(45, 1, 'E11', 'standard', 'active', '2025-05-15 02:30:50', '2025-05-15 02:30:50', NULL, NULL);

-- --------------------------------------------------------

--
-- Structure de la table `seat_plan`
--

CREATE TABLE `seat_plan` (
  `seat_plan_id` int NOT NULL,
  `plan_config` varchar(50) NOT NULL,
  `layout_details` json DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `created_by` int DEFAULT NULL,
  `updated_by` int DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3;

--
-- Déchargement des données de la table `seat_plan`
--

INSERT INTO `seat_plan` (`seat_plan_id`, `plan_config`, `layout_details`, `created_at`, `updated_at`, `created_by`, `updated_by`) VALUES
(1, '2-2', '{\"rows\": 11, \"columns\": 5}', '2025-03-02 23:03:34', '2025-05-15 13:32:12', NULL, NULL),
(2, '3-2', '{\"rows\": 8, \"columns\": 5}', '2025-03-02 23:03:34', '2025-03-02 23:03:34', NULL, NULL),
(3, '2-3', '{\"rows\": 9, \"columns\": 5}', '2025-03-02 23:03:34', '2025-03-02 23:03:34', NULL, NULL),
(4, '3-3', '{\"rows\": 7, \"columns\": 6}', '2025-03-02 23:03:34', '2025-03-02 23:03:34', NULL, NULL),
(5, '2-2-2', '{\"rows\": 10, \"columns\": 6}', '2025-03-02 23:03:34', '2025-03-02 23:03:34', NULL, NULL);

-- --------------------------------------------------------

--
-- Structure de la table `stop`
--

CREATE TABLE `stop` (
  `stop_id` int NOT NULL,
  `stop_name` varchar(255) NOT NULL,
  `address` text,
  `coordinates` point NOT NULL
) ;

--
-- Déchargement des données de la table `stop`
--

INSERT INTO `stop` (`stop_id`, `stop_name`, `address`, `coordinates`, `location_id`, `created_at`, `updated_at`, `created_by`, `updated_by`) VALUES
(1, 'Gare Routière de Cotonou', 'Rue 123, Cotonou', 0xe61000000101000000c58f31772d210340d0d556ec2f7b1940, 1, '2025-03-02 23:03:34', '2025-03-02 23:03:34', NULL, NULL),
(2, 'Gare de Parakou', 'Avenue 456, Parakou', 0xe61000000101000000840d4faf9465f33fcdcccccccc8c1840, 2, '2025-03-02 23:03:34', '2025-05-15 01:39:22', NULL, NULL),
(3, 'Gare de Natitingou', 'Rue 789, Natitingou', 0xe61000000101000000ffb27bf2b050f8bf03780b2428be2840, 3, '2025-03-02 23:03:34', '2025-05-15 01:40:03', NULL, NULL),
(4, 'Gare routière de Bohicon', 'Rue 101, Bohicon', 0xe610000001010000006666666666e60040f5dbd78173062b40, 4, '2025-03-02 23:03:34', '2025-05-15 03:24:11', NULL, NULL),
(5, 'Gare Routière d\'Abidjan', 'Avenue 202, Dakar', 0xe61000000101000000d656ec2fbb7731c01e166a4df36e2d40, 5, '2025-03-02 23:03:34', '2025-05-15 01:37:24', NULL, NULL);

-- --------------------------------------------------------

--
-- Structure de la table `ticket`
--

CREATE TABLE `ticket` (
  `ticket_id` int NOT NULL,
  `booking_id` int NOT NULL,
  `trip_id` int NOT NULL,
  `seat_id` int NOT NULL,
  `passenger_name` varchar(255) NOT NULL,
  `passenger_email` varchar(255) DEFAULT NULL,
  `passenger_phone` varchar(20) DEFAULT NULL,
  `ticket_code` varchar(255) NOT NULL,
  `ticket_status` enum('valid','cancelled','boarded') DEFAULT 'valid',
  `checked_by` int DEFAULT NULL
) ;

--
-- Déchargement des données de la table `ticket`
--

INSERT INTO `ticket` (`ticket_id`, `booking_id`, `trip_id`, `seat_id`, `passenger_name`, `passenger_email`, `passenger_phone`, `ticket_code`, `ticket_status`, `checked_by`) VALUES
(6, 1, 1, 1, 'Jean Koffi', '<EMAIL>', '+22912345678', 'TICKET001', 'valid', NULL),
(7, 1, 1, 2, 'Jean Koffi', '<EMAIL>', '+22912345678', 'TICKET002', 'valid', NULL),
(8, 2, 2, 3, 'Moussa Diop', '<EMAIL>', '+22156789012', 'TICKET003', 'valid', NULL),
(9, 3, 3, 4, 'Jean Koffi', '<EMAIL>', '+22912345678', 'TICKET004', 'cancelled', NULL);

-- --------------------------------------------------------

--
-- Structure de la table `trip`
--

CREATE TABLE `trip` (
  `trip_id` int NOT NULL,
  `route_id` int NOT NULL,
  `bus_id` int NOT NULL,
  `driver_id` int NOT NULL,
  `controller_id` int DEFAULT NULL,
  `estimated_departure_time` timestamp NOT NULL,
  `estimated_arrival_time` timestamp NOT NULL,
  `actual_departure_time` timestamp NULL DEFAULT NULL,
  `actual_arrival_time` timestamp NULL DEFAULT NULL,
  `tracking_link` varchar(255) DEFAULT NULL,
  `status` enum('planned','ongoing','completed','delayed','cancelled') DEFAULT 'planned',
  `delay_reason` text,
  `cancellation_reason` text,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `created_by` int DEFAULT NULL,
  `cancelled_by` int DEFAULT NULL,
  `updated_by` int DEFAULT NULL
) ;

--
-- Déchargement des données de la table `trip`
--

INSERT INTO `trip` (`trip_id`, `route_id`, `bus_id`, `driver_id`, `controller_id`, `estimated_departure_time`, `estimated_arrival_time`, `actual_departure_time`, `actual_arrival_time`, `tracking_link`, `status`, `delay_reason`, `cancellation_reason`, `created_at`, `updated_at`, `created_by`, `cancelled_by`, `updated_by`) VALUES
(1, 1, 1, 2, 3, '2025-05-30 05:00:00', '2025-05-30 15:30:00', NULL, NULL, 'http://tracking.example.com/trip1', 'planned', NULL, NULL, '2025-03-02 23:03:34', '2025-05-15 02:00:53', NULL, NULL, NULL),
(2, 2, 2, 2, 3, '2023-10-02 07:00:00', '2023-10-02 15:00:00', NULL, NULL, 'http://tracking.example.com/trip2', 'planned', NULL, NULL, '2025-03-02 23:03:34', '2025-03-02 23:03:34', NULL, NULL, NULL),
(3, 3, 3, 2, 3, '2023-10-03 08:00:00', '2023-10-03 15:00:00', NULL, NULL, 'http://tracking.example.com/trip3', 'planned', NULL, NULL, '2025-03-02 23:03:34', '2025-03-02 23:03:34', NULL, NULL, NULL),
(4, 4, 4, 2, 3, '2025-03-11 10:00:00', '2023-10-05 09:00:00', NULL, NULL, 'http://tracking.example.com/trip4', 'planned', NULL, NULL, '2025-03-02 23:03:34', '2025-03-09 11:43:32', NULL, NULL, NULL),
(5, 5, 5, 2, 3, '2025-05-30 13:00:00', '2023-10-06 22:00:00', NULL, NULL, 'http://tracking.example.com/trip5', 'cancelled', NULL, 'Mauvais temps', '2025-03-02 23:03:34', '2025-05-15 03:32:03', NULL, 4, NULL),
(6, 1, 1, 2, NULL, '2025-05-30 06:00:00', '2025-05-30 13:30:00', NULL, NULL, NULL, 'planned', NULL, NULL, '2025-03-03 18:44:30', '2025-05-15 03:30:39', 6, NULL, NULL),
(7, 3, 5, 2, 3, '2025-05-30 13:00:00', '2025-05-30 20:30:00', NULL, NULL, 'http://tracking.example.com/trip5', 'planned', NULL, NULL, '2025-03-07 23:10:34', '2025-05-15 03:32:41', NULL, 4, NULL),
(8, 1, 2, 2, 3, '2025-05-30 10:00:00', '2025-03-30 20:00:00', NULL, NULL, 'http://tracking.example.com/trip5', 'planned', NULL, NULL, '2025-03-07 23:12:48', '2025-05-15 00:03:26', NULL, 4, NULL);

-- --------------------------------------------------------

--
-- Structure de la table `trip_stop`
--

CREATE TABLE `trip_stop` (
  `trip_stop_id` int NOT NULL,
  `stop_id` int NOT NULL,
  `trip_id` int NOT NULL,
  `arrival_time` timestamp NOT NULL,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `created_by` int DEFAULT NULL,
  `updated_by` int DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3;

--
-- Déchargement des données de la table `trip_stop`
--

INSERT INTO `trip_stop` (`trip_stop_id`, `stop_id`, `trip_id`, `arrival_time`, `created_at`, `updated_at`, `created_by`, `updated_by`) VALUES
(1, 1, 1, '2023-10-01 06:00:00', '2025-03-02 23:03:34', '2025-03-02 23:03:34', NULL, NULL),
(2, 2, 1, '2023-10-01 08:30:00', '2025-03-02 23:03:34', '2025-03-02 23:03:34', NULL, NULL),
(3, 2, 2, '2023-10-02 07:00:00', '2025-03-02 23:03:34', '2025-03-02 23:03:34', NULL, NULL),
(4, 3, 2, '2023-10-02 15:00:00', '2025-03-02 23:03:34', '2025-03-02 23:03:34', NULL, NULL),
(5, 3, 3, '2023-10-03 08:00:00', '2025-03-02 23:03:34', '2025-03-02 23:03:34', NULL, NULL),
(6, 4, 3, '2023-10-03 15:00:00', '2025-03-02 23:03:34', '2025-03-02 23:03:34', NULL, NULL),
(7, 4, 4, '2023-10-04 09:00:00', '2025-03-02 23:03:34', '2025-03-02 23:03:34', NULL, NULL),
(8, 5, 4, '2023-10-05 09:00:00', '2025-03-02 23:03:34', '2025-03-02 23:03:34', NULL, NULL),
(9, 1, 5, '2023-10-05 10:00:00', '2025-03-02 23:03:34', '2025-03-02 23:03:34', NULL, NULL),
(10, 5, 5, '2023-10-06 22:00:00', '2025-03-02 23:03:34', '2025-03-02 23:03:34', NULL, NULL),
(11, 3, 1, '2023-10-01 06:00:00', '2025-03-19 12:58:15', '2025-03-19 12:58:15', NULL, NULL),
(12, 4, 1, '2023-10-01 06:00:00', '2025-03-19 12:58:15', '2025-03-19 12:58:15', NULL, NULL),
(13, 5, 1, '2023-10-01 06:00:00', '2025-03-19 12:58:15', '2025-03-19 12:58:15', NULL, NULL);

-- --------------------------------------------------------

--
-- Structure de la table `user`
--

CREATE TABLE `user` (
  `user_id` int NOT NULL,
  `email` varchar(255) NOT NULL,
  `phone` varchar(20) NOT NULL,
  `password_hash` varchar(255),
  `first_name` varchar(100) NOT NULL,
  `last_name` varchar(100) NOT NULL,
  `date_of_birth` date DEFAULT NULL,
  `profile_picture` varchar(255) DEFAULT NULL,
  `verification_status` enum('pending','verified','rejected') DEFAULT 'pending',
  `status` enum('active','inactive','suspended') DEFAULT 'active',
  `last_login_at` timestamp NULL DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `created_by` int DEFAULT NULL,
  `updated_by` int DEFAULT NULL
) ;

--
-- Déchargement des données de la table `user`
--

INSERT INTO `user` (`user_id`, `email`, `phone`, `password_hash`, `first_name`, `last_name`, `date_of_birth`, `profile_picture`, `verification_status`, `status`, `last_login_at`, `created_at`, `updated_at`, `created_by`, `updated_by`) VALUES
(1, '<EMAIL>', '+22912345678', 'hash1', 'Jean', 'Koffi', '1990-01-01', 'http://example.com/pic1.jpg', 'verified', 'active', NULL, '2025-03-02 23:03:34', '2025-03-02 23:03:34', NULL, NULL),
(2, '<EMAIL>', '+22623456789', 'hash2', 'Amina', 'Traoré', '1985-05-15', 'http://example.com/pic2.jpg', 'verified', 'active', NULL, '2025-03-02 23:03:34', '2025-03-02 23:03:34', NULL, NULL),
(3, '<EMAIL>', '+22834567890', 'hash3', 'Kodjo', 'Amegan', '1992-07-20', 'http://example.com/pic3.jpg', 'pending', 'active', NULL, '2025-03-02 23:03:34', '2025-03-02 23:03:34', NULL, NULL),
(4, '<EMAIL>', '+22745678901', 'hash4', 'Fatima', 'Sawadogo', '1988-03-10', 'http://example.com/pic4.jpg', 'verified', 'suspended', NULL, '2025-03-02 23:03:34', '2025-03-02 23:03:34', NULL, NULL),
(5, '<EMAIL>', '+22156789012', 'hash5', 'Moussa', 'Diop', '1995-11-25', 'http://example.com/pic5.jpg', 'rejected', 'inactive', NULL, '2025-03-02 23:03:34', '2025-03-02 23:03:34', NULL, NULL),
(6, '<EMAIL>', '+123456789', '$2y$10$ZxETdanZyZWUA36mmSdTyOx2FUHCg8b/RJx7osC8jkmE7Xtm1/onu', 'John', 'Doe', NULL, NULL, 'pending', 'active', NULL, '2025-03-03 16:34:47', '2025-03-03 16:34:47', NULL, NULL);

-- --------------------------------------------------------

--
-- Structure de la table `user_authentication`
--

CREATE TABLE `user_authentication` (
  `auth_id` int NOT NULL,
  `user_id` int NOT NULL,
  `auth_token` text NOT NULL,
  `ip_address` varchar(45) DEFAULT NULL,
  `valid_until` timestamp NULL DEFAULT NULL,
  `last_used_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ;

--
-- Déchargement des données de la table `user_authentication`
--

INSERT INTO `user_authentication` (`auth_id`, `user_id`, `auth_token`, `ip_address`, `valid_until`, `last_used_at`) VALUES
(1, 1, 'token1', '***********', '2025-03-03 23:03:34', '2025-03-02 23:03:34'),
(2, 2, 'token2', '***********', '2025-03-03 23:03:34', '2025-03-02 23:03:34'),
(3, 3, 'token3', '***********', '2025-03-03 23:03:34', '2025-03-02 23:03:34'),
(4, 4, 'token4', '***********', '2025-03-03 23:03:34', '2025-03-02 23:03:34'),
(5, 5, 'token5', '***********', '2025-03-03 23:03:34', '2025-03-02 23:03:34');

-- --------------------------------------------------------

--
-- Structure de la table `user_role`
--

CREATE TABLE `user_role` (
  `user_id` int NOT NULL,
  `role_type` enum('traveler','driver','controller','operator') NOT NULL,
  `is_active` tinyint(1) DEFAULT '1',
  `assigned_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `assigned_by` int DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3;

--
-- Déchargement des données de la table `user_role`
--

INSERT INTO `user_role` (`user_id`, `role_type`, `is_active`, `assigned_at`, `updated_at`, `assigned_by`) VALUES
(1, 'traveler', 1, '2025-03-02 23:03:34', '2025-03-02 23:03:34', NULL),
(2, 'driver', 1, '2025-03-02 23:03:34', '2025-03-02 23:03:34', NULL),
(3, 'controller', 1, '2025-03-02 23:03:34', '2025-03-02 23:03:34', NULL),
(4, 'operator', 1, '2025-03-02 23:03:34', '2025-03-02 23:03:34', NULL),
(5, 'traveler', 0, '2025-03-02 23:03:34', '2025-03-02 23:03:34', NULL),
(6, 'operator', 1, '2025-03-03 18:44:17', '2025-03-03 18:44:17', NULL);

--
-- Index pour les tables déchargées
--

--
-- Index pour la table `amenity`
--
ALTER TABLE `amenity`
  ADD PRIMARY KEY (`amenity_id`),
  ADD UNIQUE KEY `amenity_name` (`amenity_name`),
  ADD KEY `fk_amenity_created_by` (`created_by`),
  ADD KEY `fk_amenity_updated_by` (`updated_by`);

--
-- Index pour la table `booked_seat`
--
ALTER TABLE `booked_seat`
  ADD PRIMARY KEY (`booked_seat_id`),
  ADD UNIQUE KEY `seat_trip_unique` (`seat_id`,`trip_id`),
  ADD KEY `fk_booked_seat_created_by` (`created_by`),
  ADD KEY `fk_booked_seat_updated_by` (`updated_by`),
  ADD KEY `seat_id_index` (`seat_id`),
  ADD KEY `trip_id_index` (`trip_id`),
  ADD KEY `booking_id_index` (`booking_id`),
  ADD KEY `status_index` (`status`);

--
-- Index pour la table `booking`
--
ALTER TABLE `booking`
  ADD PRIMARY KEY (`booking_id`),
  ADD KEY `fk_booking_created_by` (`created_by`),
  ADD KEY `fk_booking_cancelled_by` (`cancelled_by`),
  ADD KEY `fk_booking_updated_by` (`updated_by`),
  ADD KEY `user_id_index` (`user_id`),
  ADD KEY `trip_id_index` (`trip_id`),
  ADD KEY `boarding_stop_id_index` (`boarding_stop_id`),
  ADD KEY `dropping_stop_id_index` (`dropping_stop_id`),
  ADD KEY `booking_status_index` (`booking_status`);

--
-- Index pour la table `bus`
--
ALTER TABLE `bus`
  ADD PRIMARY KEY (`bus_id`),
  ADD UNIQUE KEY `registration_number` (`registration_number`),
  ADD KEY `fk_bus_seat_plan_id` (`seat_plan_id`),
  ADD KEY `fk_bus_created_by` (`created_by`),
  ADD KEY `fk_bus_updated_by` (`updated_by`),
  ADD KEY `registration_number_index` (`registration_number`),
  ADD KEY `brand_model_index` (`brand`,`model`),
  ADD KEY `status_index` (`status`);

--
-- Index pour la table `bus_amenity`
--
ALTER TABLE `bus_amenity`
  ADD PRIMARY KEY (`bus_amenity_id`),
  ADD UNIQUE KEY `bus_amenity_unique` (`bus_id`,`amenity_id`),
  ADD KEY `fk_bus_amenity_created_by` (`created_by`),
  ADD KEY `fk_bus_amenity_updated_by` (`updated_by`),
  ADD KEY `bus_id_index` (`bus_id`),
  ADD KEY `amenity_id_index` (`amenity_id`);

--
-- Index pour la table `payment`
--
ALTER TABLE `payment`
  ADD PRIMARY KEY (`payment_id`),
  ADD KEY `fk_payment_created_by` (`created_by`),
  ADD KEY `booking_id_index` (`booking_id`),
  ADD KEY `payment_method_index` (`payment_method`),
  ADD KEY `payment_status_index` (`payment_status`);

--
-- Index pour la table `pricing`
--
ALTER TABLE `pricing`
  ADD PRIMARY KEY (`pricing_id`),
  ADD KEY `fk_pricing_created_by` (`created_by`),
  ADD KEY `fk_pricing_updated_by` (`updated_by`),
  ADD KEY `route_id_index` (`route_id`),
  ADD KEY `bus_type_index` (`bus_type`),
  ADD KEY `seat_type_index` (`seat_type`),
  ADD KEY `start_date_end_date_index` (`start_date`,`end_date`);

--
-- Index pour la table `route`
--
ALTER TABLE `route`
  ADD PRIMARY KEY (`route_id`),
  ADD KEY `fk_route_created_by` (`created_by`),
  ADD KEY `fk_route_updated_by` (`updated_by`),
  ADD KEY `departure_location_id_index` (`departure_location_id`),
  ADD KEY `destination_location_id_index` (`destination_location_id`),
  ADD KEY `status_index` (`status`);

--
-- Index pour la table `route_stop`
--
ALTER TABLE `route_stop`
  ADD PRIMARY KEY (`route_stop_id`),
  ADD UNIQUE KEY `route_stop_unique` (`route_id`,`stop_id`),
  ADD KEY `fk_route_stop_created_by` (`created_by`),
  ADD KEY `fk_route_stop_updated_by` (`updated_by`),
  ADD KEY `stop_id_index` (`stop_id`),
  ADD KEY `route_id_index` (`route_id`);

--
-- Index pour la table `seat`
--
ALTER TABLE `seat`
  ADD PRIMARY KEY (`seat_id`),
  ADD UNIQUE KEY `seat_number_bus_unique` (`seat_number`,`bus_id`),
  ADD KEY `fk_seat_created_by` (`created_by`),
  ADD KEY `fk_seat_updated_by` (`updated_by`),
  ADD KEY `bus_id_index` (`bus_id`),
  ADD KEY `seat_type_index` (`seat_type`),
  ADD KEY `status_index` (`status`);

--
-- Index pour la table `seat_plan`
--
ALTER TABLE `seat_plan`
  ADD PRIMARY KEY (`seat_plan_id`),
  ADD KEY `fk_seat_plan_created_by` (`created_by`),
  ADD KEY `fk_seat_plan_updated_by` (`updated_by`);

--
-- Index pour la table `ticket`
--
ALTER TABLE `ticket`
  ADD PRIMARY KEY (`ticket_id`),
  ADD UNIQUE KEY `ticket_code` (`ticket_code`),
  ADD UNIQUE KEY `ticket_code_unique` (`ticket_code`),
  ADD KEY `fk_ticket_checked_by` (`checked_by`),
  ADD KEY `booking_id_index` (`booking_id`),
  ADD KEY `trip_id_index` (`trip_id`),
  ADD KEY `seat_id_index` (`seat_id`),
  ADD KEY `ticket_status_index` (`ticket_status`);

--
-- Index pour la table `trip`
--
ALTER TABLE `trip`
  ADD PRIMARY KEY (`trip_id`),
  ADD KEY `fk_trip_created_by` (`created_by`),
  ADD KEY `fk_trip_cancelled_by` (`cancelled_by`),
  ADD KEY `fk_trip_updated_by` (`updated_by`),
  ADD KEY `route_id_index` (`route_id`),
  ADD KEY `bus_id_index` (`bus_id`),
  ADD KEY `driver_id_index` (`driver_id`),
  ADD KEY `controller_id_index` (`controller_id`),
  ADD KEY `status_index` (`status`);

--
-- Index pour la table `trip_stop`
--
ALTER TABLE `trip_stop`
  ADD PRIMARY KEY (`trip_stop_id`),
  ADD UNIQUE KEY `trip_stop_unique` (`trip_id`,`stop_id`),
  ADD KEY `fk_trip_stop_created_by` (`created_by`),
  ADD KEY `fk_trip_stop_updated_by` (`updated_by`),
  ADD KEY `stop_id_index` (`stop_id`),
  ADD KEY `trip_id_index` (`trip_id`);

--
-- Index pour la table `user`
--
ALTER TABLE `user`
  ADD PRIMARY KEY (`user_id`),
  ADD UNIQUE KEY `email` (`email`),
  ADD UNIQUE KEY `phone` (`phone`),
  ADD UNIQUE KEY `email_phone_unique` (`email`,`phone`),
  ADD KEY `fk_user_created_by` (`created_by`),
  ADD KEY `fk_user_updated_by` (`updated_by`),
  ADD KEY `status_index` (`status`);

--
-- Index pour la table `user_authentication`
--
ALTER TABLE `user_authentication`
  ADD PRIMARY KEY (`auth_id`),
  ADD KEY `user_id_index` (`user_id`),
  ADD KEY `auth_token_index` (`auth_token`(255));

--
-- Index pour la table `user_role`
--
ALTER TABLE `user_role`
  ADD PRIMARY KEY (`user_id`,`role_type`),
  ADD KEY `fk_user_role_assigned_by` (`assigned_by`),
  ADD KEY `role_type_index` (`role_type`);

--
-- AUTO_INCREMENT pour les tables déchargées
--

--
-- AUTO_INCREMENT pour la table `amenity`
--
ALTER TABLE `amenity`
  MODIFY `amenity_id` int NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=6;

--
-- AUTO_INCREMENT pour la table `booked_seat`
--
ALTER TABLE `booked_seat`
  MODIFY `booked_seat_id` int NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=16;

--
-- AUTO_INCREMENT pour la table `booking`
--
ALTER TABLE `booking`
  MODIFY `booking_id` int NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT pour la table `bus`
--
ALTER TABLE `bus`
  MODIFY `bus_id` int NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT pour la table `bus_amenity`
--
ALTER TABLE `bus_amenity`
  MODIFY `bus_amenity_id` int NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=6;

--
-- AUTO_INCREMENT pour la table `location`
--
ALTER TABLE `location`
  MODIFY `location_id` int NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT pour la table `payment`
--
ALTER TABLE `payment`
  MODIFY `payment_id` int NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT pour la table `pricing`
--
ALTER TABLE `pricing`
  MODIFY `pricing_id` int NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT pour la table `route`
--
ALTER TABLE `route`
  MODIFY `route_id` int NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT pour la table `route_stop`
--
ALTER TABLE `route_stop`
  MODIFY `route_stop_id` int NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT pour la table `seat`
--
ALTER TABLE `seat`
  MODIFY `seat_id` int NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=48;

--
-- AUTO_INCREMENT pour la table `seat_plan`
--
ALTER TABLE `seat_plan`
  MODIFY `seat_plan_id` int NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=6;

--
-- AUTO_INCREMENT pour la table `stop`
--
ALTER TABLE `stop`
  MODIFY `stop_id` int NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT pour la table `ticket`
--
ALTER TABLE `ticket`
  MODIFY `ticket_id` int NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT pour la table `trip`
--
ALTER TABLE `trip`
  MODIFY `trip_id` int NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT pour la table `trip_stop`
--
ALTER TABLE `trip_stop`
  MODIFY `trip_stop_id` int NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=14;

--
-- AUTO_INCREMENT pour la table `user`
--
ALTER TABLE `user`
  MODIFY `user_id` int NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT pour la table `user_authentication`
--
ALTER TABLE `user_authentication`
  MODIFY `auth_id` int NOT NULL AUTO_INCREMENT;

--
-- Contraintes pour les tables déchargées
--

--
-- Contraintes pour la table `amenity`
--
ALTER TABLE `amenity`
  ADD CONSTRAINT `fk_amenity_created_by` FOREIGN KEY (`created_by`) REFERENCES `user` (`user_id`),
  ADD CONSTRAINT `fk_amenity_updated_by` FOREIGN KEY (`updated_by`) REFERENCES `user` (`user_id`);

--
-- Contraintes pour la table `booked_seat`
--
ALTER TABLE `booked_seat`
  ADD CONSTRAINT `fk_booked_seat_booking_id` FOREIGN KEY (`booking_id`) REFERENCES `booking` (`booking_id`),
  ADD CONSTRAINT `fk_booked_seat_created_by` FOREIGN KEY (`created_by`) REFERENCES `user` (`user_id`),
  ADD CONSTRAINT `fk_booked_seat_seat_id` FOREIGN KEY (`seat_id`) REFERENCES `seat` (`seat_id`),
  ADD CONSTRAINT `fk_booked_seat_trip_id` FOREIGN KEY (`trip_id`) REFERENCES `trip` (`trip_id`),
  ADD CONSTRAINT `fk_booked_seat_updated_by` FOREIGN KEY (`updated_by`) REFERENCES `user` (`user_id`);

--
-- Contraintes pour la table `booking`
--
ALTER TABLE `booking`
  ADD CONSTRAINT `fk_booking_boarding_stop_id` FOREIGN KEY (`boarding_stop_id`) REFERENCES `stop` (`stop_id`),
  ADD CONSTRAINT `fk_booking_cancelled_by` FOREIGN KEY (`cancelled_by`) REFERENCES `user` (`user_id`),
  ADD CONSTRAINT `fk_booking_created_by` FOREIGN KEY (`created_by`) REFERENCES `user` (`user_id`),
  ADD CONSTRAINT `fk_booking_dropping_stop_id` FOREIGN KEY (`dropping_stop_id`) REFERENCES `stop` (`stop_id`),
  ADD CONSTRAINT `fk_booking_trip_id` FOREIGN KEY (`trip_id`) REFERENCES `trip` (`trip_id`),
  ADD CONSTRAINT `fk_booking_updated_by` FOREIGN KEY (`updated_by`) REFERENCES `user` (`user_id`),
  ADD CONSTRAINT `fk_booking_user_id` FOREIGN KEY (`user_id`) REFERENCES `user` (`user_id`);

--
-- Contraintes pour la table `bus`
--
ALTER TABLE `bus`
  ADD CONSTRAINT `fk_bus_created_by` FOREIGN KEY (`created_by`) REFERENCES `user` (`user_id`),
  ADD CONSTRAINT `fk_bus_seat_plan_id` FOREIGN KEY (`seat_plan_id`) REFERENCES `seat_plan` (`seat_plan_id`),
  ADD CONSTRAINT `fk_bus_updated_by` FOREIGN KEY (`updated_by`) REFERENCES `user` (`user_id`);

--
-- Contraintes pour la table `bus_amenity`
--
ALTER TABLE `bus_amenity`
  ADD CONSTRAINT `fk_bus_amenity_amenity_id` FOREIGN KEY (`amenity_id`) REFERENCES `amenity` (`amenity_id`),
  ADD CONSTRAINT `fk_bus_amenity_bus_id` FOREIGN KEY (`bus_id`) REFERENCES `bus` (`bus_id`),
  ADD CONSTRAINT `fk_bus_amenity_created_by` FOREIGN KEY (`created_by`) REFERENCES `user` (`user_id`),
  ADD CONSTRAINT `fk_bus_amenity_updated_by` FOREIGN KEY (`updated_by`) REFERENCES `user` (`user_id`);

--
-- Contraintes pour la table `payment`
--
ALTER TABLE `payment`
  ADD CONSTRAINT `fk_payment_booking_id` FOREIGN KEY (`booking_id`) REFERENCES `booking` (`booking_id`),
  ADD CONSTRAINT `fk_payment_created_by` FOREIGN KEY (`created_by`) REFERENCES `user` (`user_id`);

--
-- Contraintes pour la table `pricing`
--
ALTER TABLE `pricing`
  ADD CONSTRAINT `fk_pricing_created_by` FOREIGN KEY (`created_by`) REFERENCES `user` (`user_id`),
  ADD CONSTRAINT `fk_pricing_route_id` FOREIGN KEY (`route_id`) REFERENCES `route` (`route_id`),
  ADD CONSTRAINT `fk_pricing_updated_by` FOREIGN KEY (`updated_by`) REFERENCES `user` (`user_id`);

--
-- Contraintes pour la table `route`
--
ALTER TABLE `route`
  ADD CONSTRAINT `fk_route_created_by` FOREIGN KEY (`created_by`) REFERENCES `user` (`user_id`),
  ADD CONSTRAINT `fk_route_departure_location_id` FOREIGN KEY (`departure_location_id`) REFERENCES `location` (`location_id`),
  ADD CONSTRAINT `fk_route_destination_location_id` FOREIGN KEY (`destination_location_id`) REFERENCES `location` (`location_id`),
  ADD CONSTRAINT `fk_route_updated_by` FOREIGN KEY (`updated_by`) REFERENCES `user` (`user_id`);

--
-- Contraintes pour la table `route_stop`
--
ALTER TABLE `route_stop`
  ADD CONSTRAINT `fk_route_stop_created_by` FOREIGN KEY (`created_by`) REFERENCES `user` (`user_id`),
  ADD CONSTRAINT `fk_route_stop_route_id` FOREIGN KEY (`route_id`) REFERENCES `route` (`route_id`),
  ADD CONSTRAINT `fk_route_stop_stop_id` FOREIGN KEY (`stop_id`) REFERENCES `stop` (`stop_id`),
  ADD CONSTRAINT `fk_route_stop_updated_by` FOREIGN KEY (`updated_by`) REFERENCES `user` (`user_id`);

--
-- Contraintes pour la table `seat`
--
ALTER TABLE `seat`
  ADD CONSTRAINT `fk_seat_bus_id` FOREIGN KEY (`bus_id`) REFERENCES `bus` (`bus_id`),
  ADD CONSTRAINT `fk_seat_created_by` FOREIGN KEY (`created_by`) REFERENCES `user` (`user_id`),
  ADD CONSTRAINT `fk_seat_updated_by` FOREIGN KEY (`updated_by`) REFERENCES `user` (`user_id`);

--
-- Contraintes pour la table `seat_plan`
--
ALTER TABLE `seat_plan`
  ADD CONSTRAINT `fk_seat_plan_created_by` FOREIGN KEY (`created_by`) REFERENCES `user` (`user_id`),
  ADD CONSTRAINT `fk_seat_plan_updated_by` FOREIGN KEY (`updated_by`) REFERENCES `user` (`user_id`);

--
-- Contraintes pour la table `ticket`
--
ALTER TABLE `ticket`
  ADD CONSTRAINT `fk_ticket_booking_id` FOREIGN KEY (`booking_id`) REFERENCES `booking` (`booking_id`),
  ADD CONSTRAINT `fk_ticket_checked_by` FOREIGN KEY (`checked_by`) REFERENCES `user` (`user_id`),
  ADD CONSTRAINT `fk_ticket_seat_id` FOREIGN KEY (`seat_id`) REFERENCES `seat` (`seat_id`),
  ADD CONSTRAINT `fk_ticket_trip_id` FOREIGN KEY (`trip_id`) REFERENCES `trip` (`trip_id`);

--
-- Contraintes pour la table `trip`
--
ALTER TABLE `trip`
  ADD CONSTRAINT `fk_trip_bus_id` FOREIGN KEY (`bus_id`) REFERENCES `bus` (`bus_id`),
  ADD CONSTRAINT `fk_trip_cancelled_by` FOREIGN KEY (`cancelled_by`) REFERENCES `user` (`user_id`),
  ADD CONSTRAINT `fk_trip_controller_id` FOREIGN KEY (`controller_id`) REFERENCES `user` (`user_id`),
  ADD CONSTRAINT `fk_trip_created_by` FOREIGN KEY (`created_by`) REFERENCES `user` (`user_id`),
  ADD CONSTRAINT `fk_trip_driver_id` FOREIGN KEY (`driver_id`) REFERENCES `user` (`user_id`),
  ADD CONSTRAINT `fk_trip_route_id` FOREIGN KEY (`route_id`) REFERENCES `route` (`route_id`),
  ADD CONSTRAINT `fk_trip_updated_by` FOREIGN KEY (`updated_by`) REFERENCES `user` (`user_id`);

--
-- Contraintes pour la table `trip_stop`
--
ALTER TABLE `trip_stop`
  ADD CONSTRAINT `fk_trip_stop_created_by` FOREIGN KEY (`created_by`) REFERENCES `user` (`user_id`),
  ADD CONSTRAINT `fk_trip_stop_stop_id` FOREIGN KEY (`stop_id`) REFERENCES `stop` (`stop_id`),
  ADD CONSTRAINT `fk_trip_stop_trip_id` FOREIGN KEY (`trip_id`) REFERENCES `trip` (`trip_id`),
  ADD CONSTRAINT `fk_trip_stop_updated_by` FOREIGN KEY (`updated_by`) REFERENCES `user` (`user_id`);

--
-- Contraintes pour la table `user`
--
ALTER TABLE `user`
  ADD CONSTRAINT `fk_user_created_by` FOREIGN KEY (`created_by`) REFERENCES `user` (`user_id`),
  ADD CONSTRAINT `fk_user_updated_by` FOREIGN KEY (`updated_by`) REFERENCES `user` (`user_id`);

--
-- Contraintes pour la table `user_authentication`
--
ALTER TABLE `user_authentication`
  ADD CONSTRAINT `fk_user_authentication_user_id` FOREIGN KEY (`user_id`) REFERENCES `user` (`user_id`);

--
-- Contraintes pour la table `user_role`
--
ALTER TABLE `user_role`
  ADD CONSTRAINT `fk_user_role_assigned_by` FOREIGN KEY (`assigned_by`) REFERENCES `user` (`user_id`),
  ADD CONSTRAINT `fk_user_role_user_id` FOREIGN KEY (`user_id`) REFERENCES `user` (`user_id`);
COMMIT;

/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
