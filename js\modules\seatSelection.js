/**
 * Module for seat selection functionality
 */
import { formatUtils } from './formatUtils.js';
import { fetchSeats, fetchTripStops } from './api.js';

// Store current seats for the booking process
let currentSeats = [];

/**
 * Shows an alert message
 * @param {string} message - Message to display
 * @param {string} type - Alert type (info/error)
 */
function showAlert(message, type = "info") {
  const alertContainer = document.getElementById("alert-container");
  const alertHtml = `
    <div class="alert alert-${type === "error" ? "danger" : "info"} alert-dismissible fade show" role="alert">
      ${message}
      <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
    </div>
  `;
  alertContainer.insertAdjacentHTML('beforeend', alertHtml);

  setTimeout(() => {
    const alertElement = alertContainer.querySelector(".alert");
    if (alertElement) {
      alertElement.remove();
    }
  }, 10000);
}

/**
 * Generates a seat map
 * @param {Array} seats - Seats data
 * @param {Object} layoutDetails - Bus layout details
 * @returns {string} - HTML for seat map
 */
function generateSeatMap(seats, layoutDetails) {
  const { rows, columns } = layoutDetails;
  const seatMap = [];

  for (let row = 1; row <= rows; row++) {
    for (let col = 0; col < columns; col++) {
      const seatNumber = `${String.fromCharCode(65 + col)}${row}`;
      const seat = seats.find(s => s.seat_number === seatNumber);

      if (seat) {
        const seatClass = seat.is_available ? "available" : "occupied";
        seatMap.push(`
          <span class="seat ${seatClass}" data-seat-id="${seat.seat_id}" data-seat-number="${seat.seat_number}">
            <svg class="seat-icon" width="25" height="25" viewBox="-10 -10 120 120" xmlns="http://www.w3.org/2000/svg">
              <path d="M90.443,34.848c-2.548,0-4.613,2.065-4.613,4.614v31.534c-0.284,0.098-0.57,0.179-0.846,0.313  c-0.081,0.037-4.414,2.11-11.406,4.046c-2.226-1.561-5.054-2.257-7.933-1.7c-10.579,2.052-20.845,2.078-31.411,0.065  c-2.85-0.537-5.646,0.146-7.857,1.68c-6.969-1.933-11.286-4.014-11.414-4.076c-0.259-0.128-0.526-0.205-0.792-0.297V39.46  c0-2.547-2.065-4.614-4.614-4.614c-2.548,0-4.613,2.066-4.613,4.614v37.678c0,0.222,0.034,0.431,0.064,0.644  c0.096,2.447,1.456,4.772,3.804,5.939c0.398,0.196,5.779,2.828,14.367,5.164c1.438,2.634,3.997,4.626,7.174,5.233  c6.498,1.235,13.021,1.863,19.394,1.863c6.521,0,13.2-0.655,19.851-1.944c3.143-0.607,5.675-2.575,7.109-5.173  c8.575-2.324,13.97-4.931,14.369-5.127c2.187-1.073,3.54-3.146,3.805-5.396c0.104-0.385,0.179-0.784,0.179-1.202V39.46  C95.059,36.913,92.992,34.848,90.443,34.848z M20.733,37.154l-0.001,29.092c0.918,0.355,2.034,0.771,3.371,1.215  c3.577-1.812,7.759-2.428,11.756-1.672c9.628,1.837,18.689,1.814,28.359-0.063c4.035-0.78,8.207-0.165,11.794,1.641  c1.23-0.411,2.274-0.793,3.151-1.132l0.017-29.083c0-5.198,3.85-9.475,8.843-10.226V12.861c0-2.548-1.927-3.75-4.613-4.615  c0,0-14.627-4.23-33.165-4.23c-18.543,0-33.739,4.23-33.739,4.23c-2.619,0.814-4.614,2.065-4.614,4.615v14.066  C16.883,27.678,20.733,31.956,20.733,37.154z"/>
            </svg>
          </span>
        `);
      } else {
        seatMap.push(`<span class="seat empty"></span>`);
      }
    }
  }

  return `
    <div class="bus-layout" style="display: grid; grid-template-columns: repeat(${columns}, auto); justify-content: center; width: fit-content; gap: 5px;">
      ${seatMap.join("")}
    </div>
  `;
}

/**
 * Updates passenger details form based on selected seats
 */
function updatePassengerDetails() {
  const selectedSeats = Array.from(document.querySelectorAll(".seat.selected"));
  const passengerDetailsContainer = document.getElementById("passenger-details");

  if (!passengerDetailsContainer) return;

  if (selectedSeats.length === 0) {
    passengerDetailsContainer.innerHTML = "<p>Aucun siège sélectionné.</p>";
    return;
  }

  const formHtml = selectedSeats
    .map((seat, index) => {
      return `
        <div class="passenger-details">
          <h6>Passager ${index + 1} | <b>Siège ${seat.dataset.seatNumber}</b></h6>
          <div class="row">
            <div class="col-lg mb-3">
              <input type="text" class="form-control" name="passenger-${index}-last-name" placeholder="Nom" required>
            </div>
            <div class="col-lg mb-3">
              <input type="text" class="form-control" name="passenger-${index}-first-name" placeholder="Prénoms" required>
            </div>
          </div>
        </div>
      `;
    })
    .join("");

  passengerDetailsContainer.innerHTML = formHtml;
}

/**
 * Generates multi-step form for booking process
 * @param {Array} tripStops - Trip stops data
 * @returns {string} - HTML for multi-step form
 */
function generateMultiStepForm(tripStops) {
  const stops = tripStops;
  const boardingStops = stops.filter(stop => stop.stop_type === "boarding");
  const droppingStops = stops.filter(stop => stop.stop_type === "dropping");

  return `
    <div id="alert-container" style="position: absolute; z-index: 9999;"> </div>

    <div class="step-indicator">
      <div class="step active">1. Sièges</div>
      <div class="step">2. Embarquement</div>
      <div class="step">3. Débarquement</div>
      <div class="step">4. Passagers</div>
      <div class="step">5. Facturation</div>
      <div class="step">6. Validation</div>
    </div>
    <div id="form-step-1" class="form-step active">
      <div class="chosen-seats-details bg-light border" id="selected-seats-details">
        <p>Veuillez sélectionner au moins un siège pour continuer.</p>
      </div>
      <div class="total-amount">
        0 FCFA
        <small>(Taxes will be calculated during payment)</small>
      </div>
      <button type="button" class="btn btn-primary mt-3 w-100 next-step" data-step="1">Continuer</button>
    </div>
    <div id="form-step-2" class="form-step">
      <h6 class="text-danger">POINT D'EMBARQUEMENT</h6>
      ${boardingStops.map(stop => `
        <div class="form-check">
          <input class="form-check-input" type="radio" name="boarding" id="boarding-${stop.stop_id}" value="${stop.stop_id}">
          <label class="form-check-label" for="boarding-${stop.stop_id}">
            <span class="stop-time">${formatUtils.formatDateTime(stop.arrival_time)}</span>
            <span>${stop.stop_name}</span>
            <div class="stop-details">${stop.location_name}, ${stop.region}, ${stop.country}</div>
          </label>
        </div>
      `).join("")}
      <div class="d-flex justify-content-between">
        <button type="button" class="btn btn-secondary mt-3 prev-step" data-step="2">Précédent</button>
        <button type="button" class="btn btn-primary mt-3 next-step" data-step="2">Continuer</button>
      </div>
    </div>
    <div id="form-step-3" class="form-step">
      <h6 class="text-danger">POINT DE DÉBARQUEMENT</h6>
      ${droppingStops.map(stop => `
        <div class="form-check">
          <input class="form-check-input" type="radio" name="dropping" id="dropping-${stop.stop_id}" value="${stop.stop_id}">
          <label class="form-check-label" for="dropping-${stop.stop_id}">
            <span class="stop-time">${formatUtils.formatDateTime(stop.arrival_time)}</span>
            <span>${stop.stop_name}</span>
            <div class="stop-details">${stop.location_name}, ${stop.region}, ${stop.country}</div>
          </label>
        </div>
      `).join("")}
      <div class="d-flex justify-content-between">
        <button type="button" class="btn btn-secondary mt-3 prev-step" data-step="3">Précédent</button>
        <button type="button" class="btn btn-primary mt-3 next-step" data-step="3">Continuer</button>
      </div>
    </div>
    <div id="form-step-4" class="form-step">
      <div id="passenger-details">
        
      </div>
      <div class="d-flex justify-content-between">
        <button type="button" class="btn btn-secondary mt-3 prev-step" data-step="4">Précédent</button>
        <button type="button" class="btn btn-primary mt-3 next-step" data-step="4">Continuer</button>
      </div>
    </div>
    <div id="form-step-5" class="form-step">
      <div class="billing-details">
        <h6>Adresse de facturation</h6>
        <div class="row mt-3">
          <div class="col-lg mb-3">
            <input type="text" class="form-control" name="billing-last-name" placeholder="Nom" required>
          </div>
          <div class="col-lg mb-3">
            <input type="text" class="form-control" name="billing-first-name" placeholder="Prénoms" required>
          </div>
        </div>
        <div class="row mt-3">
          <div class="col-lg mb-3">
            <input type="email" class="form-control" name="billing-email" placeholder="Email" required>
          </div>
          <div class="col-lg mb-3">
            <input type="tel" class="form-control" name="billing-phone" placeholder="Téléphone" required>
          </div>
        </div>
        <div class="mb-3">
          <textarea class="form-control" name="billing-address" placeholder="Adresse" required></textarea>
        </div>
      </div>
      <div class="d-flex justify-content-between">
        <button type="button" class="btn btn-secondary mt-3 prev-step" data-step="5">Précédent</button>
        <button type="button" class="btn btn-primary mt-3 next-step" data-step="5">Continuer</button>
      </div>
    </div>
    <div id="form-step-6" class="form-step">
      <div class="review-details">
        <h6>Récapitulatif</h6>
        <p><strong>Sièges sélectionnés :</strong> <span id="review-seats"></span></p>
        <p><strong>Point d'embarquement :</strong> <span id="review-boarding"></span></p>
        <p><strong>Point de débarquement :</strong> <span id="review-dropping"></span></p>
        <p><strong>Passagers :</strong></p>
        <div id="review-passengers"></div>
        <p><strong>Facturation :</strong></p>
        <div id="review-billing"></div>
        <p><strong>Montant total :</strong> <span id="review-total"></span></p>
      </div>
      <div class="d-flex justify-content-between">
        <button type="button" class="btn btn-secondary mt-3 prev-step" data-step="6">Précédent</button>
        <button type="button" class="btn btn-primary mt-3" id="pay-now">Payer maintenant</button>
      </div>
    </div>
  `;
}

/**
 * Shows seat map and booking form
 * @param {HTMLElement} tripCard - Trip card element
 * @param {Array} seats - Seats data
 * @param {Object} layoutDetails - Bus layout details
 * @param {Array} tripStops - Trip stops data
 */
function showSeatMapAndForm(tripCard, seats, layoutDetails, tripStops) {
  const tripId = tripCard.querySelector(".details-tab").dataset.tripId;
  const detailsElement = document.getElementById(`details-${tripId}`);

  currentSeats = seats;

  detailsElement.style.display = "block";
  detailsElement.dataset.mode = "seats";

  detailsElement.innerHTML = `
    <div class="col-12 seat-instruction">
      Cliquez sur un siège disponible pour procéder à votre réservation.
    </div>
    <div class="row">
      <div class="bus-plan-container col-12 col-md-6 mb-3 mb-md-0">
        <div class="seat-legend">
          <div class="legend-item">
            <svg fill="#6c757d" width="25" height="25" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="-10 -10 120 120">
              <path d="M90.443,34.848c-2.548,0-4.613,2.065-4.613,4.614v31.534c-0.284,0.098-0.57,0.179-0.846,0.313  c-0.081,0.037-4.414,2.11-11.406,4.046c-2.226-1.561-5.054-2.257-7.933-1.7c-10.579,2.052-20.845,2.078-31.411,0.065  c-2.85-0.537-5.646,0.146-7.857,1.68c-6.969-1.933-11.286-4.014-11.414-4.076c-0.259-0.128-0.526-0.205-0.792-0.297V39.46  c0-2.547-2.065-4.614-4.614-4.614c-2.548,0-4.613,2.066-4.613,4.614v37.678c0,0.222,0.034,0.431,0.064,0.644  c0.096,2.447,1.456,4.772,3.804,5.939c0.398,0.196,5.779,2.828,14.367,5.164c1.438,2.634,3.997,4.626,7.174,5.233  c6.498,1.235,13.021,1.863,19.394,1.863c6.521,0,13.2-0.655,19.851-1.944c3.143-0.607,5.675-2.575,7.109-5.173  c8.575-2.324,13.97-4.931,14.369-5.127c2.187-1.073,3.54-3.146,3.805-5.396c0.104-0.385,0.179-0.784,0.179-1.202V39.46  C95.059,36.913,92.992,34.848,90.443,34.848z M20.733,37.154l-0.001,29.092c0.918,0.355,2.034,0.771,3.371,1.215  c3.577-1.812,7.759-2.428,11.756-1.672c9.628,1.837,18.689,1.814,28.359-0.063c4.035-0.78,8.207-0.165,11.794,1.641  c1.23-0.411,2.274-0.793,3.151-1.132l0.017-29.083c0-5.198,3.85-9.475,8.843-10.226V12.861c0-2.548-1.927-3.75-4.613-4.615  c0,0-14.627-4.23-33.165-4.23c-18.543,0-33.739,4.23-33.739,4.23c-2.619,0.814-4.614,2.065-4.614,4.615v14.066  C16.883,27.678,20.733,31.956,20.733,37.154z"/>
            </svg>
            <span>Disponible</span>
          </div>
          <div class="legend-item">
            <svg fill="#f8b6bc" width="25" height="25" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="-10 -10 120 120">
              <path d="M90.443,34.848c-2.548,0-4.613,2.065-4.613,4.614v31.534c-0.284,0.098-0.57,0.179-0.846,0.313  c-0.081,0.037-4.414,2.11-11.406,4.046c-2.226-1.561-5.054-2.257-7.933-1.7c-10.579,2.052-20.845,2.078-31.411,0.065  c-2.85-0.537-5.646,0.146-7.857,1.68c-6.969-1.933-11.286-4.014-11.414-4.076c-0.259-0.128-0.526-0.205-0.792-0.297V39.46  c0-2.547-2.065-4.614-4.614-4.614c-2.548,0-4.613,2.066-4.613,4.614v37.678c0,0.222,0.034,0.431,0.064,0.644  c0.096,2.447,1.456,4.772,3.804,5.939c0.398,0.196,5.779,2.828,14.367,5.164c1.438,2.634,3.997,4.626,7.174,5.233  c6.498,1.235,13.021,1.863,19.394,1.863c6.521,0,13.2-0.655,19.851-1.944c3.143-0.607,5.675-2.575,7.109-5.173  c8.575-2.324,13.97-4.931,14.369-5.127c2.187-1.073,3.54-3.146,3.805-5.396c0.104-0.385,0.179-0.784,0.179-1.202V39.46  C95.059,36.913,92.992,34.848,90.443,34.848z M20.733,37.154l-0.001,29.092c0.918,0.355,2.034,0.771,3.371,1.215  c3.577-1.812,7.759-2.428,11.756-1.672c9.628,1.837,18.689,1.814,28.359-0.063c4.035-0.78,8.207-0.165,11.794,1.641  c1.23-0.411,2.274-0.793,3.151-1.132l0.017-29.083c0-5.198,3.85-9.475,8.843-10.226V12.861c0-2.548-1.927-3.75-4.613-4.615  c0,0-14.627-4.23-33.165-4.23c-18.543,0-33.739,4.23-33.739,4.23c-2.619,0.814-4.614,2.065-4.614,4.615v14.066  C16.883,27.678,20.733,31.956,20.733,37.154z"/>
            </svg>
            <span>Occupé</span>
          </div>
          <div class="legend-item">
            <svg fill="#0d6efd" width="25" height="25" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="-10 -10 120 120">
              <path d="M90.443,34.848c-2.548,0-4.613,2.065-4.613,4.614v31.534c-0.284,0.098-0.57,0.179-0.846,0.313  c-0.081,0.037-4.414,2.11-11.406,4.046c-2.226-1.561-5.054-2.257-7.933-1.7c-10.579,2.052-20.845,2.078-31.411,0.065  c-2.85-0.537-5.646,0.146-7.857,1.68c-6.969-1.933-11.286-4.014-11.414-4.076c-0.259-0.128-0.526-0.205-0.792-0.297V39.46  c0-2.547-2.065-4.614-4.614-4.614c-2.548,0-4.613,2.066-4.613,4.614v37.678c0,0.222,0.034,0.431,0.064,0.644  c0.096,2.447,1.456,4.772,3.804,5.939c0.398,0.196,5.779,2.828,14.367,5.164c1.438,2.634,3.997,4.626,7.174,5.233  c6.498,1.235,13.021,1.863,19.394,1.863c6.521,0,13.2-0.655,19.851-1.944c3.143-0.607,5.675-2.575,7.109-5.173  c8.575-2.324,13.97-4.931,14.369-5.127c2.187-1.073,3.54-3.146,3.805-5.396c0.104-0.385,0.179-0.784,0.179-1.202V39.46  C95.059,36.913,92.992,34.848,90.443,34.848z M20.733,37.154l-0.001,29.092c0.918,0.355,2.034,0.771,3.371,1.215  c3.577-1.812,7.759-2.428,11.756-1.672c9.628,1.837,18.689,1.814,28.359-0.063c4.035-0.78,8.207-0.165,11.794,1.641  c1.23-0.411,2.274-0.793,3.151-1.132l0.017-29.083c0-5.198,3.85-9.475,8.843-10.226V12.861c0-2.548-1.927-3.75-4.613-4.615  c0,0-14.627-4.23-33.165-4.23c-18.543,0-33.739,4.23-33.739,4.23c-2.619,0.814-4.614,2.065-4.614,4.615v14.066  C16.883,27.678,20.733,31.956,20.733,37.154z"/>
            </svg>
            <span>Sélectionné</span>
          </div>
        </div>
        <div class="bus-container">
          ${generateSeatMap(seats, layoutDetails)}
        </div>
      </div>
      <div class="col-12 col-md-6 stop-form-container">
        <h5>Suivez le Procédure</h5>
        <div id="multi-step-form">
          ${generateMultiStepForm(tripStops)}
        </div>
      </div>
    </div>
  `;
  
  detailsElement.dataset.loaded = "true";

  const bookBtn = tripCard.querySelector(".book-btn");
  bookBtn.textContent = "Masquer les sièges";
  bookBtn.dataset.mode = "hide-seats";
}

/**
 * Sets up seat selection functionality
 * @param {Array} allTrips - All trips data
 */
export function setupSeatSelection(allTrips) {
  // Handle book button click
  document.addEventListener("click", async function (e) {
    const bookBtn = e.target.closest(".book-btn");
    if (!bookBtn || bookBtn.disabled) return;

    const tripId = Number(bookBtn.getAttribute("data-trip-id"));
    const tripCard = bookBtn.closest(".trip-card");
    const detailsElement = document.getElementById(`details-${tripId}`);

    if (bookBtn.dataset.mode === "hide-seats") {
      detailsElement.style.display = "none";
      bookBtn.textContent = "Choisir un siège";
      bookBtn.dataset.mode = "show-seats";
      return;
    }

    const trip = allTrips.find(t => t.trip_id === tripId);
    if (!trip) return;
    
    const { route_id, bus_id, bus_type, departure_time, arrival_time, layout_details } = trip;

    let tripStops = await fetchTripStops(tripId);
    let seats = await fetchSeats(route_id, bus_id, departure_time, arrival_time);

    showSeatMapAndForm(tripCard, seats, layout_details, tripStops);
  });

  // Handle seat selection
  document.addEventListener("click", function (e) {
    const seat = e.target.closest(".seat.available");
    if (!seat) return;

    seat.classList.toggle("selected");

    const selectedSeats = Array.from(document.querySelectorAll(".seat.selected"));
    const totalAmount = selectedSeats.reduce((total, seat) => {
      const seatId = seat.dataset.seatId;
      const seatData = currentSeats.find(s => s.seat_id == seatId);
      return total + (seatData ? parseFloat(seatData.price) : 0);
    }, 0);

    const totalAmountElement = document.querySelector(".total-amount");
    if (totalAmountElement) {
      totalAmountElement.innerHTML = `
        ${totalAmount.toLocaleString("fr-FR")} FCFA
        <small>(Taxes incluses)</small>
      `;
    }

    const detailsContainer = document.getElementById("selected-seats-details");

    if (selectedSeats.length === 0) {
      detailsContainer.innerHTML = "<p>Veuillez sélectionner au moins un siège pour continuer.</p>";
    } else {
      const detailsHtml = selectedSeats
        .map(seat => {
          const seatId = seat.dataset.seatId;
          const seatData = currentSeats.find(s => s.seat_id == seatId);
          return `<div class="chosen-seat-detail">Siège <b>${seatData.seat_number}</b></div> 
              <div class="chosen-seat-detail">${formatUtils.capitalize(seatData.seat_type)}</div>
              <div class="chosen-seat-detail pe-4"><b>${formatUtils.formatPrice(seatData.price)}</b> FCFA</div>`;
        })
        .join("");
      detailsContainer.innerHTML = detailsHtml;
    }

    updatePassengerDetails();
  });

  // Handle multi-step form navigation
  document.addEventListener("click", function (e) {
    const nextBtn = e.target.closest(".next-step");
    const prevBtn = e.target.closest(".prev-step");
    const payBtn = e.target.closest("#pay-now");

    if (nextBtn) {
      const currentStep = parseInt(nextBtn.dataset.step);
      const selectedSeats = Array.from(document.querySelectorAll(".seat.selected"));

      if (currentStep === 1 && selectedSeats.length === 0) {
        showAlert("Veuillez sélectionner au moins un siège pour continuer.", "error");
        return;
      }
      if (currentStep > 1 && selectedSeats.length === 0) {
          showAlert("Veuillez sélectionner au moins un siège pour continuer.", "error");
          return;
      }
      if (currentStep === 2 && !document.querySelector('input[name="boarding"]:checked')) {
        showAlert("Veuillez sélectionner un point d'embarquement.", "error");
        return;
      }
      if (currentStep === 3 && !document.querySelector('input[name="dropping"]:checked')) {
        showAlert("Veuillez sélectionner un point de débarquement.", "error");
        return;
      }
      if (currentStep === 4) {
        const inputs = document.querySelectorAll("#form-step-4 input");
        for (let input of inputs) {
          if (!input.value) {
            showAlert("Veuillez remplir tous les champs des passagers.", "error");
            return;
          }
        }
      }
      if (currentStep === 5) {
        const inputs = document.querySelectorAll("#form-step-5 input, #form-step-5 textarea");
        for (let input of inputs) {
          if (!input.value) {
            showAlert("Veuillez remplir tous les champs de facturation.", "error");
            return;
          }
        }
      }

      document.querySelector(`#form-step-${currentStep}`).classList.remove("active");
      document.querySelector(`#form-step-${currentStep + 1}`).classList.add("active");
      document.querySelectorAll(".step")[currentStep - 1].classList.remove("active");
      document.querySelectorAll(".step")[currentStep].classList.add("active");
    }

    if (prevBtn) {
      const currentStep = parseInt(prevBtn.dataset.step);
      document.querySelector(`#form-step-${currentStep}`).classList.remove("active");
      document.querySelector(`#form-step-${currentStep - 1}`).classList.add("active");
      document.querySelectorAll(".step")[currentStep - 1].classList.remove("active");
      document.querySelectorAll(".step")[currentStep - 2].classList.add("active");
    }

    if (payBtn) {
      handlePaymentSubmission();
    }
  });
}

/**
 * Handles payment form submission
 */
function handlePaymentSubmission() {
  const selectedSeats = Array.from(document.querySelectorAll(".seat.selected"));
  const boardingStop = document.querySelector('input[name="boarding"]:checked')?.value;
  const droppingStop = document.querySelector('input[name="dropping"]:checked')?.value;

  document.getElementById("review-seats").textContent = selectedSeats.map(seat => seat.dataset.seatNumber).join(", ");
  document.getElementById("review-boarding").textContent = document.querySelector(`input[name="boarding"]:checked + label span:nth-child(2)`).textContent;
  document.getElementById("review-dropping").textContent = document.querySelector(`input[name="dropping"]:checked + label span:nth-child(2)`).textContent;

  const passengers = [];
  selectedSeats.forEach((seat, index) => {
    const lastName = document.querySelector(`input[name="passenger-${index}-last-name"]`).value;
    const firstName = document.querySelector(`input[name="passenger-${index}-first-name"]`).value;
    const email = document.querySelector(`input[name="passenger-${index}-email"]`).value;
    const phone = document.querySelector(`input[name="passenger-${index}-phone"]`).value;
    passengers.push(`Passager ${index + 1} (Siège ${seat.dataset.seatNumber}) : ${lastName} ${firstName}, ${email}, ${phone}`);
  });
  document.getElementById("review-passengers").innerHTML = passengers.map(p => `<p>${p}</p>`).join("");

  const billingLastName = document.querySelector(`input[name="billing-last-name"]`).value;
  const billingFirstName = document.querySelector(`input[name="billing-first-name"]`).value;
  const billingEmail = document.querySelector(`input[name="billing-email"]`).value;
  const billingPhone = document.querySelector(`input[name="billing-phone"]`).value;
  const billingAddress = document.querySelector(`textarea[name="billing-address"]`).value;
  document.getElementById("review-billing").innerHTML = `
    <p>Nom : ${billingLastName}</p>
    <p>Prénoms : ${billingFirstName}</p>
    <p>Email : ${billingEmail}</p>
    <p>Téléphone : ${billingPhone}</p>
    <p>Adresse : ${billingAddress}</p>
  `;

  const totalAmount = selectedSeats.reduce((total, seat) => {
    const seatId = seat.dataset.seatId;
    const seatData = currentSeats.find(s => s.seat_id == seatId);
    return total + (seatData ? parseFloat(seatData.price) : 0);
  }, 0);
  document.getElementById("review-total").textContent = `${totalAmount.toLocaleString("fr-FR")} FCFA`;

  const params = new URLSearchParams({
    seats: selectedSeats.map(seat => seat.dataset.seatId).join(","),
    boarding: boardingStop,
    dropping: droppingStop,
    tripId: document.querySelector("#pay-now").closest(".trip-details").id.replace("details-", ""),
    total: totalAmount
  });
  window.location.href = `checkout.html?${params.toString()}`;
}
