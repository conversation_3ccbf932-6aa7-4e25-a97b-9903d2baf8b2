<?php
require_once __DIR__ . '/../Helpers/db.php';

/**
 * Modèle pour la gestion des voyages, points d'arrêt et tarifications
 */
class TripModel {
    private $db;

    public function __construct() {
        $this->db = getDB();
    }

    // --------------------------
    // Méthodes pour les Voyages (Trips)
    // --------------------------

    /** GET /v1/trips Rechercher des voyages avec filtre */
    public function searchTrips($filters = []) {
        $sql = "SELECT t.*, b.registration_number, b.brand, b.model, b.capacity,
                b.bus_photos, b.bus_type, b.year_manufactured, b.seat_plan_id, sp.layout_details,
                r.route_name, r.description, r.distance, r.duration,
                r.departure_location_id, r.destination_location_id,
                dl.location_name AS departure_location_name,
                dl.region AS departure_location_region,
                dl.country AS departure_location_country,
                dl.time_zone AS departure_location_time_zone,
                ST_X(dl.coordinates) AS departure_location_longitude,
                ST_Y(dl.coordinates) AS departure_location_latitude,
                al.location_name AS arrival_location_name,
                al.region AS arrival_location_region,
                al.country AS arrival_location_country,
                al.time_zone AS arrival_location_time_zone,
                ST_X(al.coordinates) AS arrival_location_longitude,
                ST_Y(al.coordinates) AS arrival_location_latitude,
                u_driver.first_name AS driver_first_name,
                u_driver.last_name AS driver_last_name,
                u_controller.first_name AS controller_first_name,
                u_controller.last_name AS controller_last_name
                FROM trip t
                JOIN route r ON t.route_id = r.route_id
                JOIN bus b ON t.bus_id = b.bus_id
                JOIN seat_plan sp ON b.seat_plan_id = sp.seat_plan_id
                JOIN location dl ON r.departure_location_id = dl.location_id
                JOIN location al ON r.destination_location_id = al.location_id
                LEFT JOIN user u_driver ON t.driver_id = u_driver.user_id
                LEFT JOIN user u_controller ON t.controller_id = u_controller.user_id
            WHERE t.status IN ('planned', 'ongoing')"; 
        $params = [];
        if (isset($filters['date'])) {
            $sql .= " AND DATE(t.estimated_departure_time) = :date";
            $params[':date'] = $filters['date'];
        }
        if (isset($filters['from_location_id'])) {
            $sql .= " AND r.departure_location_id = :from_location_id";
            $params[':from_location_id'] = $filters['from_location_id'];
        }
        if (isset($filters['to_location_id'])) {
            $sql .= " AND r.destination_location_id = :to_location_id";
            $params[':to_location_id'] = $filters['to_location_id'];
        }

        $sql .= " ORDER BY t.estimated_departure_time ASC";
    
        $stmt = $this->db->prepare($sql);
        $stmt->execute($params);
        return $stmt->fetchAll();
    }

    /** GET /v1/trips Liste des voyages (Operator) */
    public function getAllTrips(int $limit = 10, int $offset = 0): array {
        $stmt = $this->db->prepare("
            SELECT * FROM trip 
            ORDER BY estimated_departure_time DESC 
            LIMIT :limit OFFSET :offset
        ");
        $stmt->bindValue(':limit', $limit, PDO::PARAM_INT);
        $stmt->bindValue(':offset', $offset, PDO::PARAM_INT);
        $stmt->execute();
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }

    /** GET /v1/trips/{id} */
    public function getTripById(int $trip_id): ?array {
        $sql = "SELECT t.*, b.registration_number, b.brand, b.model, b.capacity,
                b.bus_photos, b.bus_type, b.year_manufactured, b.seat_plan_id, sp.layout_details,
                r.route_name, r.description, r.distance, r.duration,
                r.departure_location_id, r.destination_location_id,
                dl.location_name AS departure_location_name,
                dl.region AS departure_location_region,
                dl.country AS departure_location_country,
                dl.time_zone AS departure_location_time_zone,
                ST_X(dl.coordinates) AS departure_location_longitude,
                ST_Y(dl.coordinates) AS departure_location_latitude,
                al.location_name AS arrival_location_name,
                al.region AS arrival_location_region,
                al.country AS arrival_location_country,
                al.time_zone AS arrival_location_time_zone,
                ST_X(al.coordinates) AS arrival_location_longitude,
                ST_Y(al.coordinates) AS arrival_location_latitude
                FROM trip t
                JOIN route r ON t.route_id = r.route_id
                JOIN bus b ON t.bus_id = b.bus_id
                JOIN seat_plan sp ON b.seat_plan_id = sp.seat_plan_id
                JOIN location dl ON r.departure_location_id = dl.location_id
                JOIN location al ON r.destination_location_id = al.location_id
                LEFT JOIN user u_driver ON t.driver_id = u_driver.user_id
                LEFT JOIN user u_controller ON t.controller_id = u_controller.user_id
                WHERE t.trip_id = :trip_id";
        $stmt = $this->db->prepare($sql);
        $stmt->execute([':trip_id' => $trip_id]);
    
        return $stmt->fetch(PDO::FETCH_ASSOC) ?: null;
    }
    
    /** POST /v1/trips */
    public function createTrip(array $data): int {
        $sql = "INSERT INTO trip (
            route_id, 
            bus_id, 
            driver_id, 
            controller_id,
            estimated_departure_time,
            estimated_arrival_time,
            tracking_link,
            status,
            created_by
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)";

        $stmt = $this->db->prepare($sql);
        $stmt->execute([
            $data['route_id'],
            $data['bus_id'],
            $data['driver_id'],
            $data['controller_id'] ?? null,
            $data['estimated_departure_time'],
            $data['estimated_arrival_time'],
            $data['tracking_link'] ?? null,
            $data['status'] ?? 'planned',
            $data['created_by']
        ]);
        return $this->db->lastInsertId();
    }

    /** PUT /v1/trips/{id} */
    public function updateTrip(int $tripId, array $data): bool {
        $allowedFields = ['status', 'actual_departure_time', 'actual_arrival_time', 'delay_reason'];
        $setParts = [];
        $params = [':trip_id' => $tripId];

        foreach ($data as $key => $value) {
            if(in_array($key, $allowedFields)) {
                $setParts[] = "$key = :$key";
                $params[":$key"] = $value;
            }
        }

        if(empty($setParts)) return false;

        $sql = "UPDATE trip SET ".implode(', ', $setParts)." WHERE trip_id = :trip_id";
        $stmt = $this->db->prepare($sql);
        return $stmt->execute($params);
    }

    /** DELETE /v1/trips/{id} */
    public function deleteTrip(int $tripId): bool {
        $stmt = $this->db->prepare("DELETE FROM trip WHERE trip_id = ?");
        return $stmt->execute([$tripId]);
    }

    // ------------------------------
    // Méthodes pour les Points d'arrêt (TripStops)
    // ------------------------------

    /** GET /v1/trips/{id}/stops */
    public function getTripStops(int $trip_id): array {
        $stmt = $this->db->prepare("
            SELECT
                s.stop_id,
                s.stop_name,
                s.address,
                ST_X(s.coordinates) AS stop_longitude,
                ST_Y(s.coordinates) AS stop_latitude,
                rs.stop_order,
                rs.stop_type,
                l.location_name,
                l.region,
                l.country,
                l.time_zone,
                ts.arrival_time
            FROM
                trip_stop ts
            JOIN
                stop s ON ts.stop_id = s.stop_id
            JOIN
                location l ON s.location_id = l.location_id
            JOIN
                route_stop rs ON s.stop_id = rs.stop_id
            JOIN
                trip t ON ts.trip_id = t.trip_id AND rs.route_id = t.route_id
            WHERE
                ts.trip_id = :trip_id
            ORDER BY rs.stop_order
        ");
        $stmt->execute([':trip_id' => $trip_id]);
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }

    /** POST /v1/trips/{id}/stops */
    public function addTripStop(int $tripId, array $data): int {
        $sql = "INSERT INTO trip_stop (
            trip_id, 
            stop_id, 
            arrival_time,
            created_by
        ) VALUES (?, ?, ?, ?)";

        $stmt = $this->db->prepare($sql);
        $stmt->execute([
            $tripId,
            $data['stop_id'],
            $data['arrival_time'],
            $data['created_by']
        ]);
        return $this->db->lastInsertId();
    }

    /** DELETE /v1/trips/{id}/stops/{stopId} */
    public function deleteTripStop(int $tripId, int $stopId): bool {
        $stmt = $this->db->prepare("DELETE FROM trip_stop WHERE trip_id = ? AND stop_id = ?");
        return $stmt->execute([$tripId, $stopId]);
    }

    // ------------------------------
    // Méthodes pour la Tarification (Pricing)
    // ------------------------------

    /** GET /v1/pricings */
    public function getAllPricings(int $limit = 10, int $offset = 0): array {
        $stmt = $this->db->prepare("SELECT * FROM pricing LIMIT :limit OFFSET :offset");
        $stmt->bindValue(':limit', $limit, PDO::PARAM_INT);
        $stmt->bindValue(':offset', $offset, PDO::PARAM_INT);
        $stmt->execute();
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }
    
    /** GET /v1/pricings */
    public function getTripPricing(int $route_id, int $bus_id): array {
        $sql = "SELECT p.seat_type, p.price 
                FROM pricing p
                JOIN bus b ON p.bus_type = b.bus_type
                WHERE p.route_id = :route_id AND b.bus_id = :bus_id";
        
        $stmt = $this->db->prepare($sql);
        $stmt->bindValue(':route_id', $route_id, PDO::PARAM_INT);
        $stmt->bindValue(':bus_id', $bus_id, PDO::PARAM_INT);
        $stmt->execute();
        
        return $stmt->fetchAll(PDO::FETCH_KEY_PAIR);
    }

    /** POST /v1/pricings */
    public function createPricing(array $data): int {
        $sql = "INSERT INTO pricing (
            route_id,
            bus_type,
            seat_type,
            price,
            start_date,
            end_date,
            created_by
        ) VALUES (?, ?, ?, ?, ?, ?, ?)";

        $stmt = $this->db->prepare($sql);
        $stmt->execute([
            $data['route_id'] ?? null,
            $data['bus_type'],
            $data['seat_type'],
            $data['price'],
            $data['start_date'] ?? null,
            $data['end_date'] ?? null,
            $data['created_by']
        ]);
        return $this->db->lastInsertId();
    }

    /** PUT /v1/pricings/{id} */
    public function updatePricing(int $pricingId, array $data): bool {
        $allowedFields = ['price', 'start_date', 'end_date'];
        $setParts = [];
        $params = [':pricing_id' => $pricingId];

        foreach ($data as $key => $value) {
            if(in_array($key, $allowedFields)) {
                $setParts[] = "$key = :$key";
                $params[":$key"] = $value;
            }
        }

        if(empty($setParts)) return false;

        $sql = "UPDATE pricing SET ".implode(', ', $setParts)." WHERE pricing_id = :pricing_id";
        $stmt = $this->db->prepare($sql);
        return $stmt->execute($params);
    }
    
    /**
     * Obtenir la liste des sièges occupés dans un bus en particulier
     * entre l'interval (heure de départ et d'arrivée)de temps d'un voyage
     * @param int $bus_id ID du bus
     * @param int $departureTime Temps de départ
     * @param int $arrivalTime Temps d'arrivée
     * @return array Tableau des places occupées
    */
    public function getTripBookedSeats(int $bus_id, string $departureTime, string $arrivalTime): array {
        $sql = "SELECT bs.*
                FROM booked_seat bs
                JOIN trip t ON bs.trip_id = t.trip_id
                WHERE t.bus_id = :bus_id
                AND (t.estimated_departure_time < :arrivalTime
                AND t.estimated_arrival_time > :departureTime)
                AND t.status IN ('planned', 'delayed', 'ongoing')";
        $stmt = $this->db->prepare($sql);
        $stmt->execute([':bus_id' => $bus_id,
                        ':departureTime' => $departureTime,
                        ':arrivalTime' => $arrivalTime]);
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }
}