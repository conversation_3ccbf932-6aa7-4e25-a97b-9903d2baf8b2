<?php
require_once __DIR__ . '/../Helpers/jwt.php';

class AuthMiddleware {
    public static function authenticate($role = null) {
        $headers = getallheaders() ?? [];
        $token = str_replace('Bearer ', '', $headers['Authorization'] ?? $_SERVER['HTTP_AUTHORIZATION'] ?? '');

        if (empty($token)) {
            exit(sendResponse(400, ['error' => 'Jeton d\'authentification manquant ou a expiré. Veuillez vous reconnecter.']));
        }

        try {
            $payload = verifyJWT($token);
            if($role && $payload->role !== $role) {
                throw new Exception('Unauthorized');
            }
            return $payload;
        } catch (Exception $e) {
            exit(sendResponse(401, ['error' => 'L\'authentification a échoué']));
        }
    }
}

