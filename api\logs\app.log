[2025-03-03 22:26:13] [ERROR] {"message":"<PERSON>ssource non trouv\u00e9e"}
[2025-03-03 22:44:36] [ERROR] {"message":"Token invalide"}
[2025-03-03 22:44:38] [ERROR] {"message":"Token invalide"}
[2025-03-03 22:48:34] [ERROR] {"message":"Endpoint non trouv\u00e9"}
[2025-03-03 22:48:52] [INFO] Utilisateur connecté avec succès
[2025-03-03 23:03:15] [ERROR] {"message":"Token invalide"}
[2025-03-03 23:06:31] [ERROR] {"message":"Endpoint non trouv\u00e9"}
[2025-03-04 00:25:29] [ERROR] {"message":"Endpoint non trouv\u00e9"}
[2025-03-04 00:25:30] [ERROR] {"message":"Endpoint non trouv\u00e9"}
[2025-03-04 00:25:32] [ERROR] {"message":"Endpoint non trouv\u00e9"}
[2025-03-04 00:25:37] [ERROR] {"message":"Endpoint non trouv\u00e9"}
[2025-03-04 00:25:48] [ERROR] {"message":"Endpoint non trouv\u00e9"}
[2025-03-04 00:28:22] [ERROR] {"message":"R\u00e9servation non trouv\u00e9e ou non autoris\u00e9e"}
[2025-03-04 17:32:11] [ERROR] {"message":"Token requis"}
[2025-03-04 17:32:31] [ERROR] {"message":"Token invalide"}
[2025-03-04 17:36:53] [ERROR] {"message":"Ressource non trouv\u00e9e"}
[2025-03-04 17:37:07] [INFO] Utilisateur connecté avec succès
[2025-03-04 17:39:37] [ERROR] {"message":"Token requis"}
[2025-03-04 17:39:39] [ERROR] {"message":"Token requis"}
[2025-03-04 17:39:56] [ERROR] {"message":"Token requis"}
[2025-03-07 11:41:28] [ERROR] {"message":"Version API invalide"}
[2025-03-07 11:41:47] [ERROR] {"message":"Version API invalide"}
[2025-03-07 11:41:52] [ERROR] {"message":"Version API invalide"}
[2025-03-07 11:44:36] [ERROR] {"message":"Ressource non trouv\u00e9e"}
[2025-03-07 11:44:49] [ERROR] {"message":"Ressource non trouv\u00e9e"}
[2025-03-07 11:44:53] [ERROR] {"message":"Ressource non trouv\u00e9e"}
[2025-03-07 11:45:36] [ERROR] {"message":"Version API invalide"}
[2025-03-07 11:45:39] [ERROR] {"message":"Version API invalide"}
[2025-03-07 11:45:43] [ERROR] {"message":"Version API invalide"}
[2025-03-07 11:46:47] [ERROR] {"message":"Ressource non trouv\u00e9e"}
[2025-03-07 11:46:54] [ERROR] {"message":"Token invalide"}
[2025-03-07 11:47:15] [ERROR] {"message":"Token invalide"}
[2025-03-07 13:26:55] [ERROR] {"error":"Vous n'\u00eates pas autoris\u00e9 \u00e0 vous connecter avec ce r\u00f4le"}
[2025-03-07 13:27:28] [ERROR] {"token":"eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJzdWIiOjYsInJvbGUiOiJvcGVyYXRvciIsImlhdCI6MTc0MTM1NDA0OCwiZXhwIjoxNzQxNDQwNDQ4fQ.ig5LGBuHqasVEWlPoG9HK6Bz5LdQqfpl_UBtXH913iQ"}
[2025-03-07 13:46:25] [ERROR] {"message":"Le champ 'role' est requis"}
[2025-03-07 13:56:06] [ERROR] {"error":"Invalid credentials"}
[2025-03-07 13:57:26] [ERROR] {"error":"Acc\u00e8s non autoris\u00e9,  \u00e0 vous connecter avec ce r\u00f4le"}
[2025-03-07 13:57:26] [ERROR] {"error":"Invalid credentials"}
[2025-03-07 13:58:52] [ERROR] {"error":"Acc\u00e8s non autoris\u00e9,  \u00e0 vous connecter avec ce r\u00f4le"}
[2025-03-07 13:59:46] [ERROR] {"error":"Acc\u00e8s non autoris\u00e9 ! Vous n'avez pas les droits suffisants pour acc\u00e9der \u00e0 cette ressource."}
[2025-03-07 14:00:08] [ERROR] {"error":"Acc\u00e8s non autoris\u00e9 ! Vous n'avez pas les droits suffisants pour acc\u00e9der \u00e0 cette ressource."}
[2025-03-07 14:00:13] [ERROR] {"error":"Acc\u00e8s non autoris\u00e9 ! Vous n'avez pas les droits suffisants pour acc\u00e9der \u00e0 cette ressource."}
[2025-03-07 14:03:10] [ERROR] {"error":"Invalid credentials"}
[2025-03-07 14:04:42] [ERROR] {"error":"Invalid credentials"}
[2025-03-07 15:30:25] [ERROR] {"message":"Token invalide"}
[2025-03-07 15:33:20] [ERROR] {"message":"Le champ 'route_id' est requis"}
[2025-03-07 17:22:35] [ERROR] {"message":"Le champ 'from' est requis"}
[2025-03-07 17:22:49] [ERROR] {"message":"Le champ 'from' est requis"}
[2025-03-07 17:24:40] [ERROR] {"message":"Le champ 'date' doit \u00eatre une date actuelle ou future valide (YYYY-MM-DD)"}
[2025-03-07 20:56:30] [ERROR] {"message":"Version API invalide"}
[2025-03-08 11:50:48] [ERROR] {"message":"Le champ 'from' est requis"}
[2025-03-08 19:51:15] [ERROR] {"message":"Version API invalide"}
[2025-03-08 19:54:48] [ERROR] {"message":"Version API invalide"}
[2025-03-08 19:54:57] [ERROR] {"message":"Version API invalide"}
[2025-03-08 19:55:04] [ERROR] {"message":"Version API invalide"}
[2025-03-08 19:56:56] [ERROR] {"message":"Version API invalide"}
[2025-03-08 19:57:18] [ERROR] {"message":"Version API invalide"}
[2025-03-08 19:57:37] [ERROR] {"message":"Version API invalide"}
[2025-03-08 20:12:38] [ERROR] {"message":"Version API invalide"}
[2025-03-08 20:12:42] [ERROR] {"message":"Version API invalide"}
[2025-03-08 20:12:47] [ERROR] {"message":"Version API invalide"}
[2025-03-08 20:32:16] [ERROR] {"message":"Version API invalide"}
[2025-03-08 20:32:16] [ERROR] {"message":"Version API invalide"}
[2025-03-08 20:32:54] [ERROR] {"message":"Version API invalide"}
[2025-03-08 20:33:19] [ERROR] {"message":"Version API invalide"}
[2025-03-08 20:45:02] [ERROR] {"message":"Version API invalide"}
[2025-03-08 21:06:29] [ERROR] {"message":"Version API invalide"}
[2025-03-08 21:12:42] [ERROR] {"message":"Le champ 'date' doit \u00eatre une date actuelle ou future valide (YYYY-MM-DD)"}
[2025-03-08 21:14:30] [ERROR] {"message":"Version API invalide"}
[2025-03-08 21:14:58] [ERROR] {"message":"Version API invalide"}
[2025-03-08 21:15:14] [ERROR] {"message":"Version API invalide"}
[2025-03-08 21:15:14] [ERROR] {"message":"Version API invalide"}
[2025-03-08 23:01:06] [ERROR] {"message":"Version API invalide"}
[2025-03-08 23:01:25] [ERROR] {"message":"Le champ 'from' est requis"}
[2025-03-08 23:21:16] [ERROR] {"message":"Version API invalide"}
[2025-03-08 23:23:24] [ERROR] {"message":"Le champ 'date' doit \u00eatre une date actuelle ou future valide (YYYY-MM-DD)"}
[2025-03-08 23:23:34] [ERROR] {"message":"Le champ 'date' doit \u00eatre une date actuelle ou future valide (YYYY-MM-DD)"}
[2025-03-08 23:27:40] [ERROR] {"message":"Le champ 'date' doit \u00eatre une date actuelle ou future valide (YYYY-MM-DD)"}
[2025-03-08 23:28:19] [ERROR] {"message":"Version API invalide"}
[2025-03-08 23:28:20] [ERROR] {"message":"Version API invalide"}
[2025-03-08 23:28:32] [ERROR] {"message":"Version API invalide"}
[2025-03-08 23:28:34] [ERROR] {"message":"Version API invalide"}
[2025-03-08 23:31:20] [ERROR] {"message":"Le champ 'date' doit \u00eatre une date actuelle ou future valide (YYYY-MM-DD)"}
[2025-03-08 23:31:32] [ERROR] {"message":"Version API invalide"}
[2025-03-08 23:31:59] [ERROR] {"message":"Version API invalide"}
[2025-03-08 23:32:02] [ERROR] {"message":"Le champ 'date' doit \u00eatre une date actuelle ou future valide (YYYY-MM-DD)"}
[2025-03-08 23:48:10] [ERROR] {"message":"Le champ 'date' doit \u00eatre une date actuelle ou future valide (YYYY-MM-DD)"}
[2025-03-08 23:55:35] [ERROR] {"message":"Le champ 'date' doit \u00eatre une date actuelle ou future valide (YYYY-MM-DD)"}
[2025-03-08 23:55:49] [ERROR] {"message":"Le champ 'date' doit \u00eatre une date actuelle ou future valide (YYYY-MM-DD)"}
[2025-03-08 23:57:29] [ERROR] {"message":"Le champ 'date' doit \u00eatre une date actuelle ou future valide (YYYY-MM-DD)"}
[2025-03-09 00:02:48] [ERROR] {"message":"Version API invalide"}
[2025-03-09 00:06:00] [ERROR] {"message":"Le champ 'date' doit \u00eatre une date actuelle ou future valide (YYYY-MM-DD)"}
[2025-03-09 01:30:19] [ERROR] {"message":"Ressource non trouv\u00e9e"}
[2025-03-09 01:30:19] [ERROR] {"message":"Version API invalide"}
[2025-03-09 01:48:05] [ERROR] {"message":"Version API invalide"}
[2025-03-09 02:00:56] [ERROR] {"message":"Version API invalide"}
[2025-03-09 02:02:46] [ERROR] {"message":"Le champ 'date' doit \u00eatre une date actuelle ou future valide (YYYY-MM-DD)"}
[2025-03-09 11:48:46] [ERROR] {"message":"Version API invalide"}
[2025-03-09 12:02:39] [ERROR] {"message":"Version API invalide"}
[2025-03-09 17:16:21] [ERROR] {"message":"Le champ 'date' doit \u00eatre une date actuelle ou future valide (YYYY-MM-DD)"}
[2025-03-09 17:16:23] [ERROR] {"message":"Le champ 'from' est requis"}
[2025-03-09 17:25:37] [ERROR] {"message":"Le champ 'date' doit \u00eatre une date actuelle ou future valide (YYYY-MM-DD)"}
[2025-03-09 17:26:52] [ERROR] {"message":"Le champ 'date' doit \u00eatre une date actuelle ou future valide (YYYY-MM-DD)"}
[2025-03-09 17:27:26] [ERROR] {"message":"Le champ 'date' doit \u00eatre une date actuelle ou future valide (YYYY-MM-DD)"}
[2025-03-09 17:35:23] [ERROR] {"message":"Le champ 'date' doit \u00eatre une date actuelle ou future valide (YYYY-MM-DD)"}
[2025-03-09 19:23:27] [ERROR] {"message":"Le champ 'from' est requis"}
[2025-03-09 19:25:19] [ERROR] {"message":"Le champ 'from' est requis"}
[2025-03-09 19:26:46] [ERROR] {"message":"Le champ 'from' est requis"}
[2025-03-09 20:33:31] [ERROR] {"message":"Le champ 'from' est requis"}
[2025-03-09 20:33:36] [ERROR] {"message":"Le champ 'from' est requis"}
[2025-03-09 20:35:40] [ERROR] {"message":"Le champ 'from' est requis"}
[2025-03-09 20:35:52] [ERROR] {"message":"Le champ 'from' est requis"}
[2025-03-09 20:53:30] [ERROR] {"message":"Le champ 'from' est requis"}
[2025-03-10 00:46:10] [ERROR] {"message":"Version API invalide"}
[2025-03-10 14:39:17] [ERROR] {"message":"Le champ 'from' est requis"}
[2025-03-10 21:18:55] [ERROR] {"message":"Le champ 'date' doit \u00eatre une date actuelle ou future valide (YYYY-MM-DD)"}
[2025-03-10 21:30:09] [ERROR] {"message":"Le champ 'from' est requis"}
[2025-03-10 21:49:18] [ERROR] {"message":"Version API invalide"}
[2025-03-10 23:21:13] [ERROR] {"message":"Version API invalide"}
[2025-03-10 23:41:30] [ERROR] {"message":"Le champ 'from' est requis"}
[2025-03-11 00:52:23] [ERROR] {"message":"Le champ 'date' doit \u00eatre une date actuelle ou future valide (YYYY-MM-DD)"}
[2025-03-11 00:52:47] [ERROR] {"message":"Le champ 'date' doit \u00eatre une date actuelle ou future valide (YYYY-MM-DD)"}
[2025-03-11 20:51:37] [ERROR] {"message":"Version API invalide"}
[2025-03-11 21:03:47] [ERROR] {"message":"Version API invalide"}
[2025-03-11 21:05:08] [ERROR] {"message":"Version API invalide"}
[2025-03-12 00:05:54] [ERROR] {"message":"Version API invalide"}
[2025-03-12 00:38:33] [ERROR] {"message":"Le champ 'date' doit \u00eatre une date actuelle ou future valide (YYYY-MM-DD)"}
[2025-03-12 21:57:42] [ERROR] {"message":"Version API invalide"}
[2025-03-12 22:27:40] [ERROR] {"message":"Version API invalide"}
[2025-03-12 22:28:16] [ERROR] {"message":"Version API invalide"}
[2025-03-13 01:36:48] [ERROR] {"message":"Le champ 'date' doit \u00eatre une date actuelle ou future valide (YYYY-MM-DD)"}
[2025-03-13 01:45:10] [ERROR] {"message":"Version API invalide"}
[2025-03-13 02:44:17] [ERROR] {"message":"Endpoint non trouv\u00e9"}
[2025-03-13 02:44:17] [ERROR] {"message":"Le champ 'date' doit \u00eatre une date actuelle ou future valide (YYYY-MM-DD)"}
[2025-03-13 02:57:55] [ERROR] {"message":"Endpoint non trouv\u00e9"}
[2025-03-13 02:57:55] [ERROR] {"message":"Le champ 'date' doit \u00eatre une date actuelle ou future valide (YYYY-MM-DD)"}
[2025-03-13 21:29:11] [ERROR] {"message":"Endpoint non trouv\u00e9"}
[2025-03-13 21:29:11] [ERROR] {"message":"Le champ 'date' doit \u00eatre une date actuelle ou future valide (YYYY-MM-DD)"}
[2025-03-14 01:05:41] [ERROR] {"message":"Version API invalide"}
[2025-03-14 17:51:04] [ERROR] {"message":"Version API invalide"}
[2025-03-15 19:21:15] [ERROR] {"error":"Aucun Voyage ne correspond \u00e0 ces crit\u00e8res de recherche"}
[2025-03-15 19:24:09] [ERROR] {"error":"Aucun Voyage ne correspond \u00e0 ces crit\u00e8res de recherche"}
[2025-03-16 15:59:48] [ERROR] {"message":"Le champ 'from' est requis"}
[2025-03-16 16:00:12] [ERROR] {"message":"Le champ 'from' est requis"}
[2025-03-16 19:19:13] [ERROR] {"message":"Le champ 'from' est requis"}
[2025-03-16 19:26:36] [ERROR] {"message":"Le champ 'from' est requis"}
[2025-03-16 19:26:44] [ERROR] {"message":"Le champ 'from' est requis"}
[2025-03-16 19:26:47] [ERROR] {"message":"Le champ 'from' est requis"}
[2025-03-16 19:29:38] [ERROR] {"message":"Le champ 'to' est requis"}
[2025-03-16 20:03:24] [ERROR] {"message":"Le champ 'to' est requis"}
[2025-03-16 20:06:20] [ERROR] {"message":"Le champ 'to' est requis"}
[2025-03-16 20:30:33] [ERROR] {"message":"Le champ 'to' est requis"}
[2025-03-16 20:35:57] [ERROR] {"message":"Le champ 'to' est requis"}
[2025-03-16 20:49:04] [ERROR] {"message":"Le champ 'from' est requis"}
[2025-03-16 22:00:41] [ERROR] {"message":"Le champ 'date' doit \u00eatre une date actuelle ou future valide (YYYY-MM-DD)"}
[2025-03-16 22:00:42] [ERROR] {"message":"Le champ 'from' est requis"}
[2025-03-16 22:00:44] [ERROR] {"message":"Le champ 'date' doit \u00eatre une date actuelle ou future valide (YYYY-MM-DD)"}
[2025-03-16 22:00:51] [ERROR] {"message":"Le champ 'date' doit \u00eatre une date actuelle ou future valide (YYYY-MM-DD)"}
[2025-03-16 22:01:53] [ERROR] {"message":"Le champ 'from' est requis"}
[2025-03-16 22:04:11] [ERROR] {"message":"Le champ 'date' doit \u00eatre une date actuelle ou future valide (YYYY-MM-DD)"}
[2025-03-17 15:32:28] [ERROR] {"message":"Version API invalide"}
[2025-03-17 17:28:36] [ERROR] {"message":"Le champ 'date' doit \u00eatre une date actuelle ou future valide (YYYY-MM-DD)"}
[2025-03-17 17:29:36] [ERROR] {"message":"Le champ 'date' doit \u00eatre une date actuelle ou future valide (YYYY-MM-DD)"}
[2025-03-17 17:30:41] [ERROR] {"message":"Le champ 'date' doit \u00eatre une date actuelle ou future valide (YYYY-MM-DD)"}
[2025-03-17 17:32:58] [ERROR] {"message":"Le champ 'date' doit \u00eatre une date actuelle ou future valide (YYYY-MM-DD)"}
[2025-03-17 17:34:57] [ERROR] {"message":"Le champ 'date' doit \u00eatre une date actuelle ou future valide (YYYY-MM-DD)"}
[2025-03-17 17:35:36] [ERROR] {"message":"Le champ 'date' doit \u00eatre une date actuelle ou future valide (YYYY-MM-DD)"}
[2025-03-17 17:36:09] [ERROR] {"message":"Le champ 'date' doit \u00eatre une date actuelle ou future valide (YYYY-MM-DD)"}
[2025-03-17 17:36:29] [ERROR] {"message":"Le champ 'date' doit \u00eatre une date actuelle ou future valide (YYYY-MM-DD)"}
[2025-03-17 17:37:30] [ERROR] {"message":"Le champ 'date' doit \u00eatre une date actuelle ou future valide (YYYY-MM-DD)"}
[2025-03-17 17:50:01] [ERROR] {"message":"Le champ 'date' doit \u00eatre une date actuelle ou future valide (YYYY-MM-DD)"}
[2025-03-18 20:39:31] [ERROR] {"message":"Version API invalide"}
[2025-03-18 20:51:45] [ERROR] {"message":"Version API invalide"}
[2025-03-19 00:51:11] [ERROR] {"message":"Le champ 'date' doit \u00eatre une date actuelle ou future valide (YYYY-MM-DD)"}
[2025-03-19 00:52:19] [ERROR] {"message":"Le champ 'date' doit \u00eatre une date actuelle ou future valide (YYYY-MM-DD)"}
[2025-03-19 00:52:38] [ERROR] {"message":"Le champ 'date' doit \u00eatre une date actuelle ou future valide (YYYY-MM-DD)"}
[2025-03-19 02:57:05] [ERROR] {"message":"Le champ 'date' doit \u00eatre une date actuelle ou future valide (YYYY-MM-DD)"}
[2025-03-19 02:58:50] [ERROR] {"message":"Le champ 'date' doit \u00eatre une date actuelle ou future valide (YYYY-MM-DD)"}
[2025-03-19 13:25:26] [ERROR] {"message":"Version API invalide"}
[2025-03-19 13:25:50] [ERROR] {"message":"Version API invalide"}
[2025-03-19 18:22:12] [ERROR] {"message":"Le champ 'date' doit \u00eatre une date actuelle ou future valide (YYYY-MM-DD)"}
[2025-03-19 18:30:19] [ERROR] {"message":"Le champ 'date' doit \u00eatre une date actuelle ou future valide (YYYY-MM-DD)"}
[2025-03-20 13:22:27] [ERROR] {"message":"Le champ 'date' doit \u00eatre une date actuelle ou future valide (YYYY-MM-DD)"}
[2025-03-21 00:02:04] [ERROR] {"message":"Le champ 'date' doit \u00eatre une date actuelle ou future valide (YYYY-MM-DD)"}
[2025-03-21 00:04:31] [ERROR] {"message":"Le champ 'date' doit \u00eatre une date actuelle ou future valide (YYYY-MM-DD)"}
[2025-03-21 14:10:50] [ERROR] {"message":"Le champ 'date' doit \u00eatre une date actuelle ou future valide (YYYY-MM-DD)"}
[2025-03-21 23:44:06] [ERROR] {"message":"Le champ 'date' doit \u00eatre une date actuelle ou future valide (YYYY-MM-DD)"}
[2025-03-24 02:23:19] [ERROR] {"error":"Aucun Voyage ne correspond \u00e0 ces crit\u00e8res de recherche"}
[2025-03-24 02:25:24] [ERROR] {"message":"Le champ 'date' doit \u00eatre une date actuelle ou future valide (YYYY-MM-DD)"}
[2025-03-24 02:26:05] [ERROR] {"message":"Le champ 'date' doit \u00eatre une date actuelle ou future valide (YYYY-MM-DD)"}
[2025-03-24 02:26:48] [ERROR] {"message":"Le champ 'date' doit \u00eatre une date actuelle ou future valide (YYYY-MM-DD)"}
[2025-03-24 19:46:09] [ERROR] {"message":"Le champ 'date' doit \u00eatre une date actuelle ou future valide (YYYY-MM-DD)"}
[2025-03-28 10:14:14] [ERROR] {"message":"Erreur de connexion : SQLSTATE[HY000] [2002] Aucune connexion n\u2019a pu \u00eatre \u00e9tablie car l\u2019ordinateur cible l\u2019a express\u00e9ment refus\u00e9e"}
[2025-03-28 10:14:14] [ERROR] {"message":"Erreur de connexion : SQLSTATE[HY000] [2002] Aucune connexion n\u2019a pu \u00eatre \u00e9tablie car l\u2019ordinateur cible l\u2019a express\u00e9ment refus\u00e9e"}
[2025-03-28 10:14:14] [ERROR] {"message":"Erreur de connexion : SQLSTATE[HY000] [2002] Aucune connexion n\u2019a pu \u00eatre \u00e9tablie car l\u2019ordinateur cible l\u2019a express\u00e9ment refus\u00e9e"}
[2025-03-28 10:14:35] [ERROR] {"message":"Erreur de connexion : SQLSTATE[HY000] [2002] Aucune connexion n\u2019a pu \u00eatre \u00e9tablie car l\u2019ordinateur cible l\u2019a express\u00e9ment refus\u00e9e"}
[2025-03-28 10:14:35] [ERROR] {"message":"Erreur de connexion : SQLSTATE[HY000] [2002] Aucune connexion n\u2019a pu \u00eatre \u00e9tablie car l\u2019ordinateur cible l\u2019a express\u00e9ment refus\u00e9e"}
[2025-03-28 10:15:25] [ERROR] {"message":"Erreur de connexion : SQLSTATE[HY000] [2002] Aucune connexion n\u2019a pu \u00eatre \u00e9tablie car l\u2019ordinateur cible l\u2019a express\u00e9ment refus\u00e9e"}
[2025-03-28 10:15:25] [ERROR] {"message":"Erreur de connexion : SQLSTATE[HY000] [2002] Aucune connexion n\u2019a pu \u00eatre \u00e9tablie car l\u2019ordinateur cible l\u2019a express\u00e9ment refus\u00e9e"}
[2025-03-28 10:15:25] [ERROR] {"message":"Erreur de connexion : SQLSTATE[HY000] [2002] Aucune connexion n\u2019a pu \u00eatre \u00e9tablie car l\u2019ordinateur cible l\u2019a express\u00e9ment refus\u00e9e"}
[2025-03-28 10:17:58] [ERROR] {"message":"Erreur de connexion : SQLSTATE[HY000] [2002] Aucune connexion n\u2019a pu \u00eatre \u00e9tablie car l\u2019ordinateur cible l\u2019a express\u00e9ment refus\u00e9e"}
[2025-03-28 10:19:45] [ERROR] {"message":"Erreur de connexion : SQLSTATE[HY000] [2002] Aucune connexion n\u2019a pu \u00eatre \u00e9tablie car l\u2019ordinateur cible l\u2019a express\u00e9ment refus\u00e9e"}
[2025-03-28 10:19:45] [ERROR] {"message":"Erreur de connexion : SQLSTATE[HY000] [2002] Aucune connexion n\u2019a pu \u00eatre \u00e9tablie car l\u2019ordinateur cible l\u2019a express\u00e9ment refus\u00e9e"}
[2025-03-28 10:20:55] [ERROR] {"message":"Erreur de connexion : SQLSTATE[HY000] [2002] Aucune connexion n\u2019a pu \u00eatre \u00e9tablie car l\u2019ordinateur cible l\u2019a express\u00e9ment refus\u00e9e"}
[2025-03-28 10:20:55] [ERROR] {"message":"Erreur de connexion : SQLSTATE[HY000] [2002] Aucune connexion n\u2019a pu \u00eatre \u00e9tablie car l\u2019ordinateur cible l\u2019a express\u00e9ment refus\u00e9e"}
[2025-03-28 10:21:39] [ERROR] {"message":"Erreur de connexion : SQLSTATE[HY000] [2002] Aucune connexion n\u2019a pu \u00eatre \u00e9tablie car l\u2019ordinateur cible l\u2019a express\u00e9ment refus\u00e9e"}
[2025-03-28 10:23:18] [ERROR] {"message":"Erreur de connexion : SQLSTATE[HY000] [2002] Aucune connexion n\u2019a pu \u00eatre \u00e9tablie car l\u2019ordinateur cible l\u2019a express\u00e9ment refus\u00e9e"}
[2025-03-28 10:23:18] [ERROR] {"message":"Erreur de connexion : SQLSTATE[HY000] [2002] Aucune connexion n\u2019a pu \u00eatre \u00e9tablie car l\u2019ordinateur cible l\u2019a express\u00e9ment refus\u00e9e"}
[2025-04-06 16:27:20] [ERROR] {"message":"Le champ 'date' doit \u00eatre une date actuelle ou future valide (YYYY-MM-DD)"}
[2025-04-22 10:43:58] [ERROR] {"message":"Le champ 'date' doit \u00eatre une date actuelle ou future valide (YYYY-MM-DD)"}
[2025-05-10 13:01:49] [ERROR] {"message":"Version API invalide"}
[2025-05-10 13:01:50] [ERROR] {"message":"Version API invalide"}
[2025-05-10 19:07:59] [ERROR] {"message":"Version API invalide"}
[2025-05-10 19:16:06] [ERROR] {"message":"Le champ 'date' doit \u00eatre une date actuelle ou future valide (YYYY-MM-DD)"}
[2025-05-10 19:55:11] [ERROR] {"message":"Le champ 'date' doit \u00eatre une date actuelle ou future valide (YYYY-MM-DD)"}
[2025-05-14 22:50:49] [ERROR] {"message":"Version API invalide"}
[2025-05-14 22:57:22] [ERROR] {"message":"Le champ 'date' doit \u00eatre une date actuelle ou future valide (YYYY-MM-DD)"}
[2025-05-14 23:03:47] [ERROR] {"message":"Le champ 'date' doit \u00eatre une date actuelle ou future valide (YYYY-MM-DD)"}
[2025-05-14 23:05:43] [ERROR] {"message":"Le champ 'date' doit \u00eatre une date actuelle ou future valide (YYYY-MM-DD)"}
[2025-05-14 23:18:47] [ERROR] {"message":"Le champ 'date' doit \u00eatre une date actuelle ou future valide (YYYY-MM-DD)"}
[2025-05-14 23:18:52] [ERROR] {"message":"Le champ 'date' doit \u00eatre une date actuelle ou future valide (YYYY-MM-DD)"}
[2025-05-14 23:19:10] [ERROR] {"message":"Le champ 'from' est requis"}
[2025-05-14 23:19:21] [ERROR] {"message":"Le champ 'date' doit \u00eatre une date actuelle ou future valide (YYYY-MM-DD)"}
[2025-05-14 23:21:06] [ERROR] {"message":"Le champ 'date' doit \u00eatre une date actuelle ou future valide (YYYY-MM-DD)"}
[2025-05-14 23:35:27] [ERROR] {"message":"Le champ 'date' doit \u00eatre une date actuelle ou future valide (YYYY-MM-DD)"}
[2025-05-14 23:40:33] [ERROR] {"message":"Le champ 'from' est requis"}
[2025-05-14 23:40:41] [ERROR] {"message":"Le champ 'date' doit \u00eatre une date actuelle ou future valide (YYYY-MM-DD)"}
[2025-05-14 23:45:30] [ERROR] {"message":"Le champ 'date' doit \u00eatre une date actuelle ou future valide (YYYY-MM-DD)"}
[2025-05-14 23:47:34] [ERROR] {"message":"Le champ 'date' doit \u00eatre une date actuelle ou future valide (YYYY-MM-DD)"}
[2025-05-14 23:53:08] [ERROR] {"message":"Le champ 'date' doit \u00eatre une date actuelle ou future valide (YYYY-MM-DD)"}
[2025-05-15 00:00:10] [ERROR] {"message":"Le champ 'date' doit \u00eatre une date actuelle ou future valide (YYYY-MM-DD)"}
[2025-05-15 00:02:52] [ERROR] {"message":"Le champ 'date' doit \u00eatre une date actuelle ou future valide (YYYY-MM-DD)"}
[2025-05-15 00:26:48] [ERROR] {"message":"Le champ 'date' doit \u00eatre une date actuelle ou future valide (YYYY-MM-DD)"}
[2025-05-15 01:00:09] [ERROR] {"message":"Le champ 'date' doit \u00eatre une date actuelle ou future valide (YYYY-MM-DD)"}
[2025-05-15 01:26:28] [ERROR] {"message":"Version API invalide"}
[2025-05-15 02:06:23] [ERROR] {"message":"Le champ 'date' doit \u00eatre une date actuelle ou future valide (YYYY-MM-DD)"}
[2025-05-15 03:25:30] [ERROR] {"message":"Le champ 'date' doit \u00eatre une date actuelle ou future valide (YYYY-MM-DD)"}
[2025-05-15 13:11:28] [ERROR] {"message":"Version API invalide"}
