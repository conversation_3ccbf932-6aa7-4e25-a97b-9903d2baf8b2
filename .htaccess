# Configuration Apache pour EasyBus

# Activer la réécriture d'URL
RewriteEngine On

# Rediriger toutes les requêtes API vers api/index.php
RewriteCond %{REQUEST_URI} ^/bus-booking/api/
RewriteCond %{REQUEST_FILENAME} !-f
RewriteCond %{REQUEST_FILENAME} !-d
RewriteRule ^api/(.*)$ api/index.php [QSA,L]

# Configuration des en-têtes CORS pour l'API
<IfModule mod_headers.c>
    # Permettre les requêtes cross-origin pour l'API
    SetEnvIf Origin "^http(s)?://(.+\.)?(localhost|127\.0\.0\.1)(:[0-9]+)?$" CORS_ALLOW_ORIGIN=$0
    Header always set Access-Control-Allow-Origin %{CORS_ALLOW_ORIGIN}e env=CORS_ALLOW_ORIGIN
    Header always set Access-Control-Allow-Methods "GET, POST, PUT, DELETE, OPTIONS"
    Header always set Access-Control-Allow-Headers "Content-Type, Authorization, X-Requested-With"
    Header always set Access-Control-Allow-Credentials "true"
</IfModule>

# Gérer les requêtes OPTIONS (preflight CORS)
RewriteCond %{REQUEST_METHOD} OPTIONS
RewriteRule ^(.*)$ $1 [R=200,L]

# Configuration de sécurité
<IfModule mod_headers.c>
    # Sécurité des en-têtes
    Header always set X-Content-Type-Options nosniff
    Header always set X-Frame-Options DENY
    Header always set X-XSS-Protection "1; mode=block"
    Header always set Referrer-Policy "strict-origin-when-cross-origin"

    # Cache pour les ressources statiques
    <FilesMatch "\.(css|js|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$">
        Header set Cache-Control "public, max-age=31536000"
    </FilesMatch>
</IfModule>

# Protection des fichiers sensibles
<Files ".env">
    Order allow,deny
    Deny from all
</Files>

<Files "composer.json">
    Order allow,deny
    Deny from all
</Files>

<Files "composer.lock">
    Order allow,deny
    Deny from all
</Files>

# Bloquer l'accès aux dossiers vendor et node_modules
<IfModule mod_rewrite.c>
    RewriteRule ^(vendor|node_modules)/ - [F,L]
</IfModule>

# Configuration PHP (si mod_php est utilisé)
<IfModule mod_php.c>
    php_value upload_max_filesize 10M
    php_value post_max_size 10M
    php_value max_execution_time 300
    php_value max_input_time 300
    php_value memory_limit 256M

    # Désactiver l'affichage des erreurs en production
    php_flag display_errors Off
    php_flag log_errors On
</IfModule>

# Compression gzip
<IfModule mod_deflate.c>
    AddOutputFilterByType DEFLATE text/plain
    AddOutputFilterByType DEFLATE text/html
    AddOutputFilterByType DEFLATE text/xml
    AddOutputFilterByType DEFLATE text/css
    AddOutputFilterByType DEFLATE application/xml
    AddOutputFilterByType DEFLATE application/xhtml+xml
    AddOutputFilterByType DEFLATE application/rss+xml
    AddOutputFilterByType DEFLATE application/javascript
    AddOutputFilterByType DEFLATE application/x-javascript
    AddOutputFilterByType DEFLATE application/json
</IfModule>

# Compression Brotli (si mod_brotli est activé)
<IfModule mod_brotli.c>
    # Activer la compression Brotli pour les types MIME les plus courants
    AddOutputFilterByType BROTLI_COMPRESS text/html text/plain text/xml text/css application/javascript text/javascript application/json application/xml application/rss+xml application/atom+xml application/x-javascript application/x-httpd-php application/x-httpd-fastphp application/x-httpd-eruby application/x-font-ttf application/x-font-otf application/x-font-opentype application/vnd.ms-fontobject image/svg+xml image/x-icon font/truetype font/opentype font/ttf font/eot font/otf image/vnd.microsoft.icon
</IfModule>

# Pages d'erreur personnalisées
ErrorDocument 404 /bus-booking/404.html
ErrorDocument 500 /bus-booking/500.html

# Optimisation des performances
<IfModule mod_expires.c>
    ExpiresActive On

    # Images
    ExpiresByType image/jpg "access plus 1 year"
    ExpiresByType image/jpeg "access plus 1 year"
    ExpiresByType image/gif "access plus 1 year"
    ExpiresByType image/png "access plus 1 year"
    ExpiresByType image/svg+xml "access plus 1 year"
    ExpiresByType image/x-icon "access plus 1 year"

    # CSS et JavaScript
    ExpiresByType text/css "access plus 1 month"
    ExpiresByType application/javascript "access plus 1 month"
    ExpiresByType application/x-javascript "access plus 1 month"

    # Fonts
    ExpiresByType font/woff "access plus 1 year"
    ExpiresByType font/woff2 "access plus 1 year"
    ExpiresByType application/font-woff "access plus 1 year"
    ExpiresByType application/font-woff2 "access plus 1 year"

    # HTML
    ExpiresByType text/html "access plus 1 hour"

    # JSON et XML
    ExpiresByType application/json "access plus 0 seconds"
    ExpiresByType application/xml "access plus 0 seconds"
    ExpiresByType text/xml "access plus 0 seconds"
</IfModule>
