<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Tableau de bord - EasyBus</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="css/style.css" rel="stylesheet">
    <style>
        .dashboard-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 2rem 0;
        }
        .stats-card {
            background: white;
            border-radius: 12px;
            padding: 1.5rem;
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
            transition: transform 0.3s;
            border-left: 4px solid #0d6efd;
        }
        .stats-card:hover {
            transform: translateY(-5px);
        }
        .stats-icon {
            width: 50px;
            height: 50px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.5rem;
            margin-bottom: 1rem;
        }
        .booking-card {
            background: white;
            border-radius: 12px;
            padding: 1.5rem;
            margin-bottom: 1rem;
            box-shadow: 0 3px 10px rgba(0,0,0,0.05);
            border-left: 4px solid #28a745;
        }
        .booking-status {
            padding: 0.25rem 0.75rem;
            border-radius: 20px;
            font-size: 0.8rem;
            font-weight: 600;
        }
        .status-confirmed { background-color: #d4edda; color: #155724; }
        .status-pending { background-color: #fff3cd; color: #856404; }
        .status-cancelled { background-color: #f8d7da; color: #721c24; }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-light bg-white shadow-sm">
        <div class="container">
            <a class="navbar-brand fw-bold" href="index.html">
                <i class="fas fa-bus text-primary me-2"></i>EasyBus
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav ms-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="index.html">Accueil</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="dashboard.html">Tableau de bord</a>
                    </li>
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" id="userDropdown" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-user-circle me-1"></i>
                            <span id="userName">Mon compte</span>
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="#" onclick="showProfile()"><i class="fas fa-user me-2"></i>Profil</a></li>
                            <li><a class="dropdown-item" href="#" onclick="showBookings()"><i class="fas fa-ticket-alt me-2"></i>Mes réservations</a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="#" onclick="logout()"><i class="fas fa-sign-out-alt me-2"></i>Déconnexion</a></li>
                        </ul>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Header du tableau de bord -->
    <div class="dashboard-header">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-md-8">
                    <h1 class="mb-2">Bienvenue, <span id="welcomeName">Utilisateur</span> !</h1>
                    <p class="mb-0">Gérez vos réservations et découvrez de nouvelles destinations</p>
                </div>
                <div class="col-md-4 text-md-end">
                    <a href="index.html" class="btn btn-light btn-lg">
                        <i class="fas fa-plus me-2"></i>Nouvelle réservation
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Contenu principal -->
    <div class="container my-5">
        <!-- Statistiques -->
        <div class="row mb-5">
            <div class="col-md-3 mb-4">
                <div class="stats-card">
                    <div class="stats-icon bg-primary text-white">
                        <i class="fas fa-ticket-alt"></i>
                    </div>
                    <h3 class="mb-1" id="totalBookings">0</h3>
                    <p class="text-muted mb-0">Réservations totales</p>
                </div>
            </div>
            <div class="col-md-3 mb-4">
                <div class="stats-card">
                    <div class="stats-icon bg-success text-white">
                        <i class="fas fa-calendar-check"></i>
                    </div>
                    <h3 class="mb-1" id="upcomingTrips">0</h3>
                    <p class="text-muted mb-0">Voyages à venir</p>
                </div>
            </div>
            <div class="col-md-3 mb-4">
                <div class="stats-card">
                    <div class="stats-icon bg-info text-white">
                        <i class="fas fa-check-circle"></i>
                    </div>
                    <h3 class="mb-1" id="completedTrips">0</h3>
                    <p class="text-muted mb-0">Voyages terminés</p>
                </div>
            </div>
            <div class="col-md-3 mb-4">
                <div class="stats-card">
                    <div class="stats-icon bg-warning text-white">
                        <i class="fas fa-coins"></i>
                    </div>
                    <h3 class="mb-1" id="totalSpent">0 FCFA</h3>
                    <p class="text-muted mb-0">Total dépensé</p>
                </div>
            </div>
        </div>

        <div class="row">
            <!-- Prochains voyages -->
            <div class="col-lg-8 mb-4">
                <div class="card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5 class="mb-0"><i class="fas fa-calendar-alt me-2"></i>Prochains voyages</h5>
                        <a href="#" class="btn btn-sm btn-outline-primary" onclick="showBookings()">Voir tout</a>
                    </div>
                    <div class="card-body">
                        <div id="upcomingTripsContainer">
                            <div class="text-center py-4">
                                <i class="fas fa-spinner fa-spin fa-2x text-muted"></i>
                                <p class="mt-2 text-muted">Chargement...</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Réservations récentes -->
            <div class="col-lg-4 mb-4">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0"><i class="fas fa-history me-2"></i>Activité récente</h5>
                    </div>
                    <div class="card-body">
                        <div id="recentBookingsContainer">
                            <div class="text-center py-4">
                                <i class="fas fa-spinner fa-spin fa-2x text-muted"></i>
                                <p class="mt-2 text-muted">Chargement...</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Actions rapides -->
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0"><i class="fas fa-bolt me-2"></i>Actions rapides</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-3 mb-3">
                                <a href="index.html" class="btn btn-outline-primary w-100">
                                    <i class="fas fa-search mb-2 d-block"></i>
                                    Rechercher un voyage
                                </a>
                            </div>
                            <div class="col-md-3 mb-3">
                                <a href="#" onclick="showBookings()" class="btn btn-outline-success w-100">
                                    <i class="fas fa-list mb-2 d-block"></i>
                                    Mes réservations
                                </a>
                            </div>
                            <div class="col-md-3 mb-3">
                                <a href="#" onclick="showProfile()" class="btn btn-outline-info w-100">
                                    <i class="fas fa-user-edit mb-2 d-block"></i>
                                    Modifier profil
                                </a>
                            </div>
                            <div class="col-md-3 mb-3">
                                <a href="search-booking.html" class="btn btn-outline-warning w-100">
                                    <i class="fas fa-search-plus mb-2 d-block"></i>
                                    Retrouver réservation
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Messages d'alerte -->
    <div class="position-fixed top-0 end-0 p-3" style="z-index: 11">
        <div id="alertContainer"></div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="js/auth.js"></script>
    <script src="js/dashboard.js"></script>
</body>
</html>
