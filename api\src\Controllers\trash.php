<?php
{
    "trips": {
        "trip_id": 6,
        "route_id": 1,
        "bus_id": 1,
        "driver_id": 2,
        "controller_id": null,
        "estimated_departure_time": "2025-03-10 08:00:00",
        "estimated_arrival_time": "2025-03-10 12:30:00",
        "tracking_link": null,
        
        "route_name": "Cotonou - Lomé",
        "description": "Trajet entre Cotonou et Lomé",
        "departure_location_id": 1,
        "destination_location_id": 2,
        "distance": "150.00",
        "duration": "02:30:00",
        
        "registration_number": "BJ-1234-AB",
        "brand": "Toyota",
        "model": "Coaster",
        "capacity": 30,
        "bus_photos": "[\"http://example.com/bus1.jpg\"]",
        "bus_type": "standard",
        "layout_details": "{\"rows\": 10, \"columns\": 4}",
        
        "driver_first_name": "<PERSON><PERSON>",
        "driver_last_name": "<PERSON><PERSON><PERSON><PERSON>",
        
        "controller_first_name": null,
        "controller_last_name": null
        
        "route_name": "<PERSON><PERSON>ou - Lomé",
        "description": "Traj<PERSON> entre Cotonou et Lomé",
        "departure_location_id": 1,
        "destination_location_id": 2,
        "distance": "150.00",
        "duration": "02:30:00",
    }
}
    
if (!$trip) {
    return null;
}

// Récupérer les arrêts du trajet
$stops = $this->getTripStops($trip_id);

// Récupérer les commodités du bus
$amenities = $this->getBusAmenities($trip['bus_id']);

// Récupérer le plan des sièges
$seat_plan = $this->getSeatPlan($trip['bus_id']);

// Récupérer les sièges avec leur disponibilité (selon votre logique)
$seats = $this->getSeatsWithAvailability($trip['bus_id'], $trip_id);

// Récupérer les prix (selon votre logique)
$pricing = $this->getPricing($trip['route_id'], $trip['bus_type']);

// Construire la réponse JSON
$response = [
    'trip_id' => $trip['trip_id'],
    'route' => [
        'route_id' => $trip['route_id'],
        'route_name' => $trip['route_name'],
        'departure_location' => $trip['departure_location'],
        'destination_location' => $trip['destination_location'],
        'stops' => $stops
    ],
    'bus' => [
        'bus_id' => $trip['bus_id'],
        'registration_number' => $trip['registration_number'],
        'brand' => $trip['brand'],
        'model' => $trip['model'],
        'capacity' => $trip['capacity'],
        'bus_type' => $trip['bus_type'],
        'amenities' => $amenities,
        'seat_plan' => $seat_plan
    ],
    'driver' => [
        'first_name' => $trip['driver_first_name'],
        'last_name' => $trip['driver_last_name']
    ],
    'controller' => [
        'first_name' => $trip['controller_first_name'],
        'last_name' => $trip['controller_last_name']
    ],
    'estimated_departure_time' => $trip['estimated_departure_time'],
    'estimated_arrival_time' => $trip['estimated_arrival_time'],
    'pricing' => $pricing,
    'available_seats_count' => $this->getAvailableSeatsCount($seats),
    'available_seats' => $seats
];

public function getTripDetails(int $trip_id): void 
    {
        $response = $this->tripModel->getTripById($trip_id);
        
        if(!$response) {
            sendResponse(404, ['error' => 'Aucun Voyage ne correspond à cet ID']);
        }
        sendResponse(200, ['trips' => $response]);
    }