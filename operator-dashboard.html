<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Tableau de bord Opérateur - EasyBus</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="css/style.css" rel="stylesheet">
    <style>
        .sidebar {
            min-height: 100vh;
            background: linear-gradient(180deg, #2c3e50 0%, #34495e 100%);
            color: white;
        }
        .sidebar .nav-link {
            color: rgba(255,255,255,0.8);
            padding: 0.75rem 1rem;
            border-radius: 8px;
            margin-bottom: 0.25rem;
            transition: all 0.3s;
        }
        .sidebar .nav-link:hover,
        .sidebar .nav-link.active {
            background-color: rgba(255,255,255,0.1);
            color: white;
        }
        .main-content {
            background-color: #f8f9fa;
            min-height: 100vh;
        }
        .stats-card {
            background: white;
            border-radius: 12px;
            padding: 1.5rem;
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
            transition: transform 0.3s;
            border-left: 4px solid #0d6efd;
        }
        .stats-card:hover {
            transform: translateY(-5px);
        }
        .stats-icon {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.5rem;
            margin-bottom: 1rem;
        }
        .chart-container {
            background: white;
            border-radius: 12px;
            padding: 1.5rem;
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
        }
        .alert-item {
            background: white;
            border-radius: 8px;
            padding: 1rem;
            margin-bottom: 0.5rem;
            border-left: 4px solid #ffc107;
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- Sidebar -->
            <div class="col-md-3 col-lg-2 sidebar p-0">
                <div class="p-3">
                    <h4 class="text-center mb-4">
                        <i class="fas fa-bus me-2"></i>EasyBus
                        <small class="d-block fs-6 text-muted">Opérateur</small>
                    </h4>
                    
                    <nav class="nav flex-column">
                        <a class="nav-link active" href="#" onclick="showSection('dashboard')">
                            <i class="fas fa-tachometer-alt me-2"></i>Tableau de bord
                        </a>
                        <a class="nav-link" href="#" onclick="showSection('bookings')">
                            <i class="fas fa-ticket-alt me-2"></i>Réservations
                        </a>
                        <a class="nav-link" href="#" onclick="showSection('trips')">
                            <i class="fas fa-route me-2"></i>Voyages
                        </a>
                        <a class="nav-link" href="#" onclick="showSection('buses')">
                            <i class="fas fa-bus me-2"></i>Bus
                        </a>
                        <a class="nav-link" href="#" onclick="showSection('reports')">
                            <i class="fas fa-chart-bar me-2"></i>Rapports
                        </a>
                        <a class="nav-link" href="#" onclick="showSection('validation')">
                            <i class="fas fa-qrcode me-2"></i>Validation tickets
                        </a>
                        <hr class="my-3">
                        <a class="nav-link" href="#" onclick="logout()">
                            <i class="fas fa-sign-out-alt me-2"></i>Déconnexion
                        </a>
                    </nav>
                </div>
            </div>

            <!-- Contenu principal -->
            <div class="col-md-9 col-lg-10 main-content">
                <!-- Header -->
                <div class="bg-white shadow-sm p-3 mb-4">
                    <div class="d-flex justify-content-between align-items-center">
                        <h2 class="mb-0" id="pageTitle">Tableau de bord</h2>
                        <div class="d-flex align-items-center">
                            <span class="me-3">Bonjour, <strong id="operatorName">Opérateur</strong></span>
                            <div class="dropdown">
                                <button class="btn btn-outline-secondary dropdown-toggle" type="button" data-bs-toggle="dropdown">
                                    <i class="fas fa-user-circle"></i>
                                </button>
                                <ul class="dropdown-menu">
                                    <li><a class="dropdown-item" href="#"><i class="fas fa-user me-2"></i>Profil</a></li>
                                    <li><a class="dropdown-item" href="#"><i class="fas fa-cog me-2"></i>Paramètres</a></li>
                                    <li><hr class="dropdown-divider"></li>
                                    <li><a class="dropdown-item" href="#" onclick="logout()"><i class="fas fa-sign-out-alt me-2"></i>Déconnexion</a></li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Section Tableau de bord -->
                <div id="dashboardSection" class="content-section">
                    <!-- Statistiques -->
                    <div class="row mb-4">
                        <div class="col-md-3 mb-3">
                            <div class="stats-card">
                                <div class="stats-icon bg-primary text-white">
                                    <i class="fas fa-ticket-alt"></i>
                                </div>
                                <h3 class="mb-1" id="totalBookingsToday">0</h3>
                                <p class="text-muted mb-0">Réservations aujourd'hui</p>
                            </div>
                        </div>
                        <div class="col-md-3 mb-3">
                            <div class="stats-card">
                                <div class="stats-icon bg-success text-white">
                                    <i class="fas fa-bus"></i>
                                </div>
                                <h3 class="mb-1" id="activeTrips">0</h3>
                                <p class="text-muted mb-0">Voyages en cours</p>
                            </div>
                        </div>
                        <div class="col-md-3 mb-3">
                            <div class="stats-card">
                                <div class="stats-icon bg-warning text-white">
                                    <i class="fas fa-coins"></i>
                                </div>
                                <h3 class="mb-1" id="revenueToday">0 FCFA</h3>
                                <p class="text-muted mb-0">Revenus aujourd'hui</p>
                            </div>
                        </div>
                        <div class="col-md-3 mb-3">
                            <div class="stats-card">
                                <div class="stats-icon bg-info text-white">
                                    <i class="fas fa-users"></i>
                                </div>
                                <h3 class="mb-1" id="totalPassengers">0</h3>
                                <p class="text-muted mb-0">Passagers aujourd'hui</p>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <!-- Graphique des revenus -->
                        <div class="col-lg-8 mb-4">
                            <div class="chart-container">
                                <h5 class="mb-3"><i class="fas fa-chart-line me-2"></i>Évolution des revenus (7 derniers jours)</h5>
                                <canvas id="revenueChart" height="100"></canvas>
                            </div>
                        </div>

                        <!-- Alertes et notifications -->
                        <div class="col-lg-4 mb-4">
                            <div class="chart-container">
                                <h5 class="mb-3"><i class="fas fa-bell me-2"></i>Alertes</h5>
                                <div id="alertsContainer">
                                    <div class="text-center py-4">
                                        <i class="fas fa-spinner fa-spin fa-2x text-muted"></i>
                                        <p class="mt-2 text-muted">Chargement...</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Voyages du jour -->
                    <div class="row">
                        <div class="col-12">
                            <div class="chart-container">
                                <h5 class="mb-3"><i class="fas fa-calendar-day me-2"></i>Voyages d'aujourd'hui</h5>
                                <div id="todayTripsContainer">
                                    <div class="text-center py-4">
                                        <i class="fas fa-spinner fa-spin fa-2x text-muted"></i>
                                        <p class="mt-2 text-muted">Chargement...</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Section Validation des tickets -->
                <div id="validationSection" class="content-section" style="display: none;">
                    <div class="row justify-content-center">
                        <div class="col-md-8">
                            <div class="chart-container text-center">
                                <h3 class="mb-4"><i class="fas fa-qrcode me-2"></i>Validation des tickets</h3>
                                
                                <div class="mb-4">
                                    <div class="input-group input-group-lg">
                                        <input type="text" class="form-control" id="ticketCodeInput" 
                                               placeholder="Scanner ou saisir le code du ticket" autofocus>
                                        <button class="btn btn-primary" type="button" onclick="validateTicket()">
                                            <i class="fas fa-check me-2"></i>Valider
                                        </button>
                                    </div>
                                </div>

                                <div id="validationResult" class="mt-4"></div>

                                <div class="mt-4">
                                    <button class="btn btn-outline-secondary me-2" onclick="startQRScanner()">
                                        <i class="fas fa-camera me-2"></i>Scanner QR Code
                                    </button>
                                    <button class="btn btn-outline-info" onclick="showValidationHistory()">
                                        <i class="fas fa-history me-2"></i>Historique
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Autres sections (cachées par défaut) -->
                <div id="bookingsSection" class="content-section" style="display: none;">
                    <h3>Gestion des réservations</h3>
                    <p>Section en cours de développement...</p>
                </div>

                <div id="tripsSection" class="content-section" style="display: none;">
                    <h3>Gestion des voyages</h3>
                    <p>Section en cours de développement...</p>
                </div>

                <div id="busesSection" class="content-section" style="display: none;">
                    <h3>Gestion des bus</h3>
                    <p>Section en cours de développement...</p>
                </div>

                <div id="reportsSection" class="content-section" style="display: none;">
                    <h3>Rapports et analyses</h3>
                    <p>Section en cours de développement...</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Messages d'alerte -->
    <div class="position-fixed top-0 end-0 p-3" style="z-index: 11">
        <div id="alertContainer"></div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script src="js/auth.js"></script>
    <script src="js/operator-dashboard.js"></script>
</body>
</html>
