<?php
require_once __DIR__ . '/../../vendor/autoload.php';
require_once __DIR__ . '/../Helpers/response.php';

class Router {
    private $router;
    
    public function __construct() {
        $this->router = new AltoRouter();
        $this->setupRoutes();
    }
    
    private function setupRoutes() {
        $basePath = '/bus-booking/api/v1';
        $this->router->setBasePath($basePath);
        
        // Routes d'authentification
        $this->router->map('POST', '/auth/register', 'UserController#register');
        $this->router->map('POST', '/auth/login', 'UserController#login');
        $this->router->map('POST', '/auth/logout', 'UserController#logout');
        $this->router->map('POST', '/auth/refresh', 'UserController#refreshToken');
        
        // Routes utilisateurs
        $this->router->map('GET', '/users/profile', 'UserController#getProfile');
        $this->router->map('PUT', '/users/profile', 'UserController#updateProfile');
        $this->router->map('GET', '/users/dashboard', 'UserController#getDashboard');
        
        // Routes de localisation
        $this->router->map('GET', '/locations', 'LocationController#getLocations');
        $this->router->map('GET', '/locations/[i:id]', 'LocationController#getLocationById');
        
        // Routes des trajets
        $this->router->map('GET', '/routes', 'RouteController#getAllRoutes');
        $this->router->map('GET', '/routes/[i:id]', 'RouteController#getRouteById');
        $this->router->map('POST', '/routes', 'RouteController#createRoute');
        $this->router->map('PUT', '/routes/[i:id]', 'RouteController#updateRoute');
        $this->router->map('DELETE', '/routes/[i:id]', 'RouteController#deleteRoute');
        
        // Routes des voyages
        $this->router->map('GET', '/trips', 'TripController#searchTrips');
        $this->router->map('GET', '/trips/[i:id]', 'TripController#getTripDetails');
        $this->router->map('GET', '/trips/[i:id]/stops', 'TripController#getTripStops');
        $this->router->map('GET', '/trips/[i:id]/seats', 'TripController#getTripSeats');
        $this->router->map('POST', '/trips', 'TripController#createTrip');
        $this->router->map('PUT', '/trips/[i:id]', 'TripController#updateTrip');
        $this->router->map('DELETE', '/trips/[i:id]', 'TripController#cancelTrip');
        
        // Routes des sièges
        $this->router->map('GET', '/seats/availability', 'TripController#getSeatsAvailability');
        
        // Routes des réservations
        $this->router->map('GET', '/bookings', 'BookingController#getUserBookings');
        $this->router->map('GET', '/bookings/[i:id]', 'BookingController#getBookingById');
        $this->router->map('POST', '/bookings', 'BookingController#createBooking');
        $this->router->map('PUT', '/bookings/[i:id]', 'BookingController#updateBooking');
        $this->router->map('DELETE', '/bookings/[i:id]', 'BookingController#cancelBooking');
        
        // Routes de réservation sans compte
        $this->router->map('POST', '/bookings/guest', 'BookingController#createGuestBooking');
        $this->router->map('GET', '/bookings/search', 'BookingController#searchBookings');
        
        // Routes des paiements
        $this->router->map('POST', '/payments', 'PaymentController#createPayment');
        $this->router->map('GET', '/payments/[i:id]', 'PaymentController#getPaymentStatus');
        $this->router->map('POST', '/payments/fedapay/webhook', 'PaymentController#handleFedaPayWebhook');
        
        // Routes des équipements
        $this->router->map('GET', '/amenities', 'BusController#getAllAmenities');
        
        // Routes des bus (opérateurs)
        $this->router->map('GET', '/buses', 'BusController#getAllBuses');
        $this->router->map('GET', '/buses/[i:id]', 'BusController#getBusById');
        $this->router->map('POST', '/buses', 'BusController#createBus');
        $this->router->map('PUT', '/buses/[i:id]', 'BusController#updateBus');
        $this->router->map('DELETE', '/buses/[i:id]', 'BusController#deleteBus');
        
        // Routes des tickets
        $this->router->map('GET', '/tickets/[*:code]', 'TicketController#getTicketByCode');
        $this->router->map('POST', '/tickets/validate', 'TicketController#validateTicket');
        $this->router->map('GET', '/tickets/[i:id]/qr', 'TicketController#generateQRCode');
        
        // Routes du tableau de bord opérateur
        $this->router->map('GET', '/operator/dashboard', 'OperatorController#getDashboard');
        $this->router->map('GET', '/operator/bookings', 'OperatorController#getBookings');
        $this->router->map('GET', '/operator/reports', 'OperatorController#getReports');
        $this->router->map('GET', '/operator/analytics', 'OperatorController#getAnalytics');
        
        // Routes des notifications
        $this->router->map('POST', '/notifications/email', 'NotificationController#sendEmail');
        $this->router->map('GET', '/notifications/templates', 'NotificationController#getTemplates');
    }
    
    public function dispatch() {
        $match = $this->router->match();
        
        if ($match === false) {
            sendResponse(404, ['message' => 'Endpoint non trouvé']);
            return;
        }
        
        list($controller, $action) = explode('#', $match['target']);
        $params = $match['params'];
        
        $controllerFile = __DIR__ . "/../Controllers/{$controller}.php";
        if (!file_exists($controllerFile)) {
            sendResponse(500, ['message' => 'Contrôleur non trouvé']);
            return;
        }
        
        require_once $controllerFile;
        
        if (!class_exists($controller)) {
            sendResponse(500, ['message' => 'Classe contrôleur non trouvée']);
            return;
        }
        
        $controllerInstance = new $controller();
        
        if (!method_exists($controllerInstance, $action)) {
            sendResponse(500, ['message' => 'Méthode non trouvée']);
            return;
        }
        
        // Récupérer les données de la requête
        $data = json_decode(file_get_contents('php://input'), true) ?? [];
        $queryParams = $_GET ?? [];
        
        // Fusionner les paramètres d'URL avec les données
        $allParams = array_merge($params, $data, $queryParams);
        
        try {
            call_user_func_array([$controllerInstance, $action], [$allParams]);
        } catch (Exception $e) {
            sendResponse(500, ['message' => 'Erreur interne du serveur', 'error' => $e->getMessage()]);
        }
    }
}
