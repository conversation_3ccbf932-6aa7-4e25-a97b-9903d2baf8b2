/**
 * API module for handling all API requests
 */

const API_BASE_URL = 'https://bus-booking.test/api/v1';

/**
 * Cache for API responses
 */
const cache = {
  trips: [],
  amenities: null,
  tripDetails: {},
  seats: new Map(),
  tripStops: {},
  cities: []
};

/**
 * Fetches trips based on search parameters
 * @param {string} date - Departure date
 * @param {string} departureId - Departure location ID
 * @param {string} destinationId - Destination location ID
 * @returns {Promise<Array>} - Array of trips
 */
export async function fetchTrips(date, departureId, destinationId) {
  if (cache.trips.length > 0) {
    return cache.trips;
  }

  try {
    const response = await fetch(`${API_BASE_URL}/trips?date=${date}&from=${departureId}&to=${destinationId}`, {
      headers: {
        'Accept-Encoding': 'br, gzip, deflate'
      }
    });

    if (!response.ok) {
      throw new Error("Impossible de charger les trajets, échec de la requête.");
    }

    const data = await response.json();
    cache.trips = data.trips;
    return cache.trips;
  } catch (error) {
    console.error(error);
    return null;
  }
}

/**
 * Fetches available amenities
 * @returns {Promise<Array>} - Array of amenities
 */
export async function fetchAmenities() {
  if (cache.amenities) {
    return cache.amenities;
  }

  try {
    const response = await fetch(`${API_BASE_URL}/amenities`, {
      headers: {
        'Accept-Encoding': 'br, gzip, deflate'
      }
    });

    if (!response.ok) {
      throw new Error("Erreur lors de la récupération des commodités.");
    }

    const data = await response.json();
    cache.amenities = data;
    return data;
  } catch (error) {
    console.error("Erreur lors du chargement des commodités:", error);
    return [];
  }
}

/**
 * Fetches details for a specific trip
 * @param {number} tripId - Trip ID
 * @returns {Promise<Object>} - Trip details
 */
export async function fetchTripDetails(tripId) {
  if (cache.tripDetails[tripId]) {
    return cache.tripDetails[tripId];
  }

  try {
    const response = await fetch(`${API_BASE_URL}/trips/${tripId}`);
    const data = await response.json();
    
    cache.tripDetails[tripId] = data;
    return data;
  } catch (error) {
    console.error("Erreur lors du chargement des détails du trajet", error);
    throw error;
  }
}

/**
 * Fetches seats for a specific trip
 * @param {number} route_id - Route ID
 * @param {number} bus_id - Bus ID
 * @param {string} departure_time - Departure time
 * @param {string} arrival_time - Arrival time
 * @returns {Promise<Array>} - Array of seats
 */
export async function fetchSeats(route_id, bus_id, departure_time, arrival_time) {
  const cacheKey = `${route_id}-${bus_id}-${departure_time}-${arrival_time}`;
  if (cache.seats.has(cacheKey)) {
    return cache.seats.get(cacheKey);
  }

  try {
    const response = await fetch(`${API_BASE_URL}/seats?route_id=${route_id}&bus_id=${bus_id}&estimated_departure_time=${departure_time}&estimated_arrival_time=${arrival_time}`, {
      headers: {
        'Accept-Encoding': 'br, gzip, deflate'
      }
    });
    const seats = await response.json();
    cache.seats.set(cacheKey, seats);
    return seats;
  } catch (error) {
    console.error("Erreur lors du chargement des sièges", error);
    throw error;
  }
}

/**
 * Fetches stops for a specific trip
 * @param {number} tripId - Trip ID
 * @returns {Promise<Array>} - Array of stops
 */
export async function fetchTripStops(tripId) {
  let tripStops = cache.tripDetails[tripId]?.route?.stops;

  if (tripStops) {
    return tripStops;
  }

  if (cache.tripStops[tripId]) {
    return cache.tripStops[tripId];
  }

  try {
    const response = await fetch(`${API_BASE_URL}/trips/${tripId}/stops`);
    tripStops = await response.json();

    cache.tripStops[tripId] = tripStops;

    return tripStops;
  } catch (error) {
    console.error("Erreur lors du chargement des arrêts du trajet", error);
    throw error;
  }
}

/**
 * Fetches available cities/locations
 * @returns {Promise<Array>} - Array of cities
 */
export async function fetchCities() {
  if (cache.cities.length > 0) {
    return cache.cities;
  }

  try {
    const response = await fetch(`${API_BASE_URL}/locations/`);
    if (!response.ok) {
      throw new Error(`Erreur ${response.status}: ${response.statusText} lors du chargement des villes`);
    }
    const data = await response.json();
    cache.cities = data;
    return cache.cities;
  } catch (error) {
    console.error("Erreur:", error);
    throw error;
  }
}
