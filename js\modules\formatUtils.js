/**
 * Utility functions for formatting data
 */
export const formatUtils = {
  /**
   * Formats a date-time string to display only hours and minutes
   * @param {string} dateTimeStr - ISO date-time string
   * @returns {string} Formatted time (HH:MM)
   */
  formatDateTime(dateTimeStr) {
    const date = new Date(dateTimeStr);
    if (isNaN(date.getTime())) return "Heure invalide";
    const hours = date.getHours().toString().padStart(2, "0");
    const minutes = date.getMinutes().toString().padStart(2, "0");
    return `${hours}:${minutes}`;
  },

  /**
   * Formats a duration string (HH:MM) to a readable format
   * @param {string} durationStr - Duration in format "HH:MM"
   * @returns {string} Formatted duration (Xh Ymin)
   */
  formatDuration(durationStr) {
    const [hours, minutes] = durationStr.split(":");
    const h = parseInt(hours);
    const m = parseInt(minutes);
    return `${h}h ${m}min`;
  },

  /**
   * Formats a date-time string with custom options
   * @param {string} dateTimeStr - ISO date-time string
   * @param {Object} options - Date formatting options
   * @returns {string} Formatted date
   */
  formatDateOptions(dateTimeStr, options) {
    const date = new Date(dateTimeStr);
    if (isNaN(date.getTime())) return "Date invalide";
    return date.toLocaleDateString("fr-FR", options);
  },

  /**
   * Formats a date-time string to a full date
   * @param {string} dateTimeStr - ISO date-time string
   * @returns {string} Formatted date
   */
  formatDate(dateTimeStr) {
    const options = {
      weekday: "long",
      day: "numeric",
      month: "long",
      year: "numeric",
    };
    return this.formatDateOptions(dateTimeStr, options);
  },

  /**
   * Formats a date-time string to a full date and time
   * @param {string} dateTimeStr - ISO date-time string
   * @returns {string} Formatted date and time
   */
  formatFullDateTime(dateTimeStr) {
    const options = {
      weekday: "long",
      day: "numeric",
      month: "long",
      year: "numeric",
      hour: "2-digit",
      minute: "2-digit",
    };
    return this.formatDateOptions(dateTimeStr, options);
  },

  /**
   * Gets the appropriate Font Awesome icon for an amenity
   * @param {string} amenityName - Name of the amenity
   * @returns {string} Font Awesome icon class
   */
  getAmenityIcon(amenityName) {
    const icons = {
      "wi-fi": "fa-wifi",
      "climatisation": "fa-snowflake",
      "toilettes": "fa-toilet",
      "prise électrique": "fa-plug",
      "usb": "fa-usb",
      "divertissement": "fa-tv",
      "boissons": "fa-mug-hot",
      "collation": "fa-cookie-bite",
    };
    return icons[amenityName.toLowerCase()] || "fa-check-circle";
  },

  /**
   * Capitalizes the first letter of a string
   * @param {string} str - String to capitalize
   * @returns {string} Capitalized string
   */
  capitalize(str) {
    if (!str) return str;
    return str.charAt(0).toUpperCase() + str.slice(1);
  },

  /**
   * Formats a price with French locale
   * @param {number|string} price - Price to format
   * @returns {string} Formatted price
   */
  formatPrice(price) {
    const num = parseFloat(price);
    if (isNaN(num)) return "Prix invalide";
    return num.toLocaleString("fr-FR");
  },
};

/**
 * Parses a duration string (HH:MM) to minutes
 * @param {string} durationStr - Duration in format "HH:MM"
 * @returns {number} Total minutes
 */
export function parseDuration(durationStr) {
  const parts = durationStr.split(":").map(Number);
  
  if (parts.length !== 2 || isNaN(parts[0]) || isNaN(parts[1])) {
    return 0;
  }
  
  const [hours, minutes] = parts;
  return hours * 60 + minutes;
}
