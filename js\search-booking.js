// Configuration de l'API
const API_BASE_URL = 'api/v1';

// Variables globales
let currentBookings = [];
let selectedBooking = null;

// Initialisation
document.addEventListener('DOMContentLoaded', function() {
    initializeSearchPage();
});

// Initialiser la page de recherche
function initializeSearchPage() {
    // Gérer la soumission du formulaire
    document.getElementById('searchForm').addEventListener('submit', handleSearch);
    
    // Vérifier s'il y a des paramètres dans l'URL
    const urlParams = new URLSearchParams(window.location.search);
    const searchTerm = urlParams.get('search');
    
    if (searchTerm) {
        document.getElementById('searchInput').value = searchTerm;
        performSearch(searchTerm);
    }
}

// Gérer la recherche
async function handleSearch(e) {
    e.preventDefault();
    
    const searchInput = document.getElementById('searchInput');
    const searchTerm = searchInput.value.trim();
    
    if (!searchTerm) {
        showAlert('Veuillez saisir un email ou un numéro de téléphone', 'warning');
        return;
    }
    
    // Validation basique
    if (!isValidEmailOrPhone(searchTerm)) {
        showAlert('Format d\'email ou de téléphone invalide', 'warning');
        return;
    }
    
    await performSearch(searchTerm);
}

// Effectuer la recherche
async function performSearch(searchTerm) {
    try {
        // Désactiver le bouton de recherche
        const submitBtn = document.querySelector('#searchForm button[type="submit"]');
        submitBtn.disabled = true;
        submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Recherche...';
        
        // Déterminer si c'est un email ou un téléphone
        const isEmail = searchTerm.includes('@');
        const searchParams = isEmail ? { email: searchTerm } : { phone: searchTerm };
        
        // Appel API
        const response = await apiRequest('bookings/search?' + new URLSearchParams(searchParams));
        
        if (response.bookings && response.bookings.length > 0) {
            currentBookings = response.bookings;
            displayBookings(currentBookings);
            showResultsSection();
        } else {
            showAlert('Aucune réservation trouvée avec ces informations', 'info');
        }
        
    } catch (error) {
        console.error('Erreur lors de la recherche:', error);
        showAlert(error.message || 'Erreur lors de la recherche', 'danger');
    } finally {
        // Réactiver le bouton
        const submitBtn = document.querySelector('#searchForm button[type="submit"]');
        submitBtn.disabled = false;
        submitBtn.innerHTML = '<i class="fas fa-search me-2"></i>Rechercher mes réservations';
    }
}

// Afficher la section des résultats
function showResultsSection() {
    document.getElementById('resultsSection').style.display = 'block';
    document.getElementById('resultsSection').scrollIntoView({ behavior: 'smooth' });
}

// Afficher les réservations
function displayBookings(bookings) {
    const container = document.getElementById('bookingsContainer');
    
    if (!bookings || bookings.length === 0) {
        container.innerHTML = `
            <div class="text-center py-5">
                <i class="fas fa-inbox fa-3x text-muted mb-3"></i>
                <h4 class="text-muted">Aucune réservation trouvée</h4>
                <p class="text-muted">Vérifiez vos informations et réessayez</p>
            </div>
        `;
        return;
    }
    
    let html = '';
    bookings.forEach(booking => {
        const departureDate = new Date(booking.estimated_departure_time);
        const isPast = departureDate < new Date();
        
        html += `
            <div class="booking-result">
                <div class="row align-items-center">
                    <div class="col-md-8">
                        <div class="d-flex justify-content-between align-items-start mb-2">
                            <h5 class="mb-0">${booking.route_name}</h5>
                            <span class="booking-status status-${booking.booking_status}">
                                ${getStatusText(booking.booking_status)}
                            </span>
                        </div>
                        
                        <div class="row">
                            <div class="col-sm-6">
                                <small class="text-muted d-block">Départ</small>
                                <strong>${formatDateTime(booking.estimated_departure_time)}</strong>
                                <br><small class="text-muted">${booking.boarding_stop}</small>
                            </div>
                            <div class="col-sm-6">
                                <small class="text-muted d-block">Arrivée</small>
                                <strong>${formatDateTime(booking.estimated_arrival_time)}</strong>
                                <br><small class="text-muted">${booking.dropping_stop}</small>
                            </div>
                        </div>
                        
                        <div class="mt-2">
                            <small class="text-muted">Réservation #${booking.booking_id} • ${formatDate(booking.created_at)}</small>
                        </div>
                    </div>
                    
                    <div class="col-md-4 text-md-end">
                        <div class="fw-bold text-primary mb-2">${formatCurrency(booking.total_amount)}</div>
                        
                        ${isPast ? '<span class="badge bg-secondary mb-2">Terminé</span>' : ''}
                        ${!isPast && booking.booking_status === 'confirmed' ? '<span class="badge bg-success mb-2">À venir</span>' : ''}
                        
                        <div class="mt-2">
                            <button class="btn btn-sm btn-outline-primary me-2" onclick="viewBookingDetails(${booking.booking_id})">
                                <i class="fas fa-eye me-1"></i>Détails
                            </button>
                            
                            ${booking.booking_status === 'confirmed' && !isPast ? `
                                <button class="btn btn-sm btn-outline-success" onclick="downloadTickets(${booking.booking_id})">
                                    <i class="fas fa-ticket-alt me-1"></i>Tickets
                                </button>
                            ` : ''}
                        </div>
                    </div>
                </div>
            </div>
        `;
    });
    
    container.innerHTML = html;
}

// Voir les détails d'une réservation
async function viewBookingDetails(bookingId) {
    try {
        selectedBooking = currentBookings.find(b => b.booking_id == bookingId);
        
        if (!selectedBooking) {
            showAlert('Réservation non trouvée', 'error');
            return;
        }
        
        // Charger les tickets de la réservation
        const response = await apiRequest(`bookings/${bookingId}/tickets`);
        const tickets = response.tickets || [];
        
        // Afficher le modal avec les détails
        displayBookingModal(selectedBooking, tickets);
        
    } catch (error) {
        console.error('Erreur lors du chargement des détails:', error);
        showAlert('Erreur lors du chargement des détails', 'danger');
    }
}

// Afficher le modal des détails
function displayBookingModal(booking, tickets) {
    const modalBody = document.getElementById('bookingModalBody');
    
    let ticketsHtml = '';
    if (tickets && tickets.length > 0) {
        ticketsHtml = `
            <h6 class="mt-4 mb-3">Tickets</h6>
            <div class="row">
        `;
        
        tickets.forEach(ticket => {
            ticketsHtml += `
                <div class="col-md-6 mb-3">
                    <div class="card">
                        <div class="card-body">
                            <h6 class="card-title">${ticket.passenger_name}</h6>
                            <p class="card-text">
                                <strong>Siège:</strong> ${ticket.seat_number}<br>
                                <strong>Code:</strong> <code>${ticket.ticket_code}</code>
                            </p>
                        </div>
                    </div>
                </div>
            `;
        });
        
        ticketsHtml += '</div>';
    }
    
    modalBody.innerHTML = `
        <div class="row">
            <div class="col-md-6">
                <h6>Informations du voyage</h6>
                <table class="table table-borderless">
                    <tr>
                        <td><strong>Trajet:</strong></td>
                        <td>${booking.route_name}</td>
                    </tr>
                    <tr>
                        <td><strong>Départ:</strong></td>
                        <td>${formatDateTime(booking.estimated_departure_time)}</td>
                    </tr>
                    <tr>
                        <td><strong>Arrivée:</strong></td>
                        <td>${formatDateTime(booking.estimated_arrival_time)}</td>
                    </tr>
                    <tr>
                        <td><strong>Embarquement:</strong></td>
                        <td>${booking.boarding_stop}</td>
                    </tr>
                    <tr>
                        <td><strong>Débarquement:</strong></td>
                        <td>${booking.dropping_stop}</td>
                    </tr>
                </table>
            </div>
            <div class="col-md-6">
                <h6>Informations de réservation</h6>
                <table class="table table-borderless">
                    <tr>
                        <td><strong>Numéro:</strong></td>
                        <td>#${booking.booking_id}</td>
                    </tr>
                    <tr>
                        <td><strong>Statut:</strong></td>
                        <td><span class="booking-status status-${booking.booking_status}">${getStatusText(booking.booking_status)}</span></td>
                    </tr>
                    <tr>
                        <td><strong>Date de réservation:</strong></td>
                        <td>${formatDate(booking.created_at)}</td>
                    </tr>
                    <tr>
                        <td><strong>Montant total:</strong></td>
                        <td class="fw-bold text-primary">${formatCurrency(booking.total_amount)}</td>
                    </tr>
                </table>
            </div>
        </div>
        ${ticketsHtml}
    `;
    
    // Afficher le modal
    const modal = new bootstrap.Modal(document.getElementById('bookingModal'));
    modal.show();
}

// Télécharger les tickets
function downloadTickets(bookingId = null) {
    const id = bookingId || (selectedBooking ? selectedBooking.booking_id : null);
    
    if (!id) {
        showAlert('Aucune réservation sélectionnée', 'warning');
        return;
    }
    
    // Simuler le téléchargement (à implémenter côté serveur)
    showAlert('Fonctionnalité de téléchargement en cours de développement', 'info');
}

// Nouvelle recherche
function newSearch() {
    document.getElementById('resultsSection').style.display = 'none';
    document.getElementById('searchInput').value = '';
    document.getElementById('searchInput').focus();
    currentBookings = [];
}

// Fonctions utilitaires
function isValidEmailOrPhone(input) {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    const phoneRegex = /^[\+]?[0-9\s\-\(\)]{8,}$/;
    
    return emailRegex.test(input) || phoneRegex.test(input);
}

function formatCurrency(amount) {
    return new Intl.NumberFormat('fr-FR', {
        style: 'decimal',
        minimumFractionDigits: 0
    }).format(amount) + ' FCFA';
}

function formatDate(dateString) {
    const date = new Date(dateString);
    return date.toLocaleDateString('fr-FR', {
        day: '2-digit',
        month: '2-digit',
        year: 'numeric'
    });
}

function formatDateTime(dateString) {
    const date = new Date(dateString);
    return date.toLocaleDateString('fr-FR', {
        day: '2-digit',
        month: '2-digit',
        hour: '2-digit',
        minute: '2-digit'
    });
}

function getStatusText(status) {
    const statusMap = {
        'confirmed': 'Confirmé',
        'pending': 'En attente',
        'cancelled': 'Annulé'
    };
    return statusMap[status] || status;
}

// Fonction pour faire des requêtes API (simplifiée)
async function apiRequest(endpoint, options = {}) {
    const url = `${API_BASE_URL}/${endpoint}`;
    
    const defaultOptions = {
        headers: {
            'Content-Type': 'application/json'
        }
    };
    
    const finalOptions = {
        ...defaultOptions,
        ...options,
        headers: {
            ...defaultOptions.headers,
            ...options.headers
        }
    };
    
    try {
        const response = await fetch(url, finalOptions);
        const data = await response.json();
        
        if (!response.ok) {
            throw new Error(data.message || 'Erreur de requête');
        }
        
        return data;
    } catch (error) {
        console.error('Erreur API:', error);
        throw error;
    }
}

// Fonction pour afficher les alertes
function showAlert(message, type = 'success') {
    const alertContainer = document.getElementById('alertContainer');
    const alertId = 'alert-' + Date.now();
    
    const alertHTML = `
        <div class="alert alert-${type} alert-dismissible fade show" role="alert" id="${alertId}">
            <i class="fas fa-${type === 'success' ? 'check-circle' : type === 'danger' ? 'exclamation-triangle' : 'info-circle'} me-2"></i>
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    `;
    
    alertContainer.insertAdjacentHTML('beforeend', alertHTML);
    
    // Auto-suppression après 5 secondes
    setTimeout(() => {
        const alert = document.getElementById(alertId);
        if (alert) {
            alert.remove();
        }
    }, 5000);
}
