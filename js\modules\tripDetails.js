/**
 * Module for displaying trip details
 */
import { formatUtils } from './formatUtils.js';
import { fetchTripDetails } from './api.js';

/**
 * Gets status badge HTML
 * @param {string} status - Trip status
 * @returns {string} - HTML for badge
 */
function getStatusBadge(status) {
  const statusMap = {
    "planned": { label: "Planifié", class: "bg-primary" },
    "ongoing": { label: "En cours", class: "bg-info" },
    "completed": { label: "Terminé", class: "bg-success" },
    "delayed": { label: "Retardé", class: "bg-warning" },
    "cancelled": { label: "Annulé", class: "bg-danger" },
  };

  const { label, class: badgeClass } = statusMap[status] || { label: status, class: "" };
  return `<div class="badge ${badgeClass}">${label}</div>`;
}

/**
 * Renders stops for a trip
 * @param {Array} stops - List of stops
 * @returns {string} - HTML for stops
 */
function renderStops(stops) {
  return stops.map((stop) => `
    <div class="timeline-stop ${stop.stop_type}">
      <div class="fw-bold">${stop.stop_name}</div>
      <div>${stop.location_name}, ${stop.region}, ${stop.country}</div>
      <div class="text-muted">${formatUtils.formatFullDateTime(stop.arrival_time)}</div>
      <div class="small text-muted">${stop.address}</div>
      <div class="small">
        <span class="badge bg-light text-dark">
          ${stop.stop_type === "boarding" ? "Embarquement" :
          stop.stop_type === "dropping" ? "Débarquement" : "Arrêt"}
        </span>
      </div>
    </div>
  `).join('');
}

/**
 * Renders bus information
 * @param {Object} bus - Bus object
 * @returns {string} - HTML for bus info
 */
function renderBusInfo(bus) {
  return `
    <div class="col-md-6">
      <h5 class="mb-3">Informations sur le bus</h5>
      <div class="card mb-3">
        <div class="card-body">
          <div class="d-flex justify-content-between mb-2">
            <div>Marque & Modèle:</div>
            <div class="fw-bold">${bus.brand} ${bus.model}</div>
          </div>
          <div class="d-flex justify-content-between mb-2">
            <div>Immatriculation:</div>
            <div class="fw-bold">${bus.registration_number}</div>
          </div>
          <div class="d-flex justify-content-between mb-2">
            <div>Type:</div>
            <div class="fw-bold">${bus.bus_type === "standard" ? "Standard" : "VIP"}</div>
          </div>
          <div class="d-flex justify-content-between mb-2">
            <div>Année:</div>
            <div class="fw-bold">${bus.year_manufactured}</div>
          </div>
          <div class="d-flex justify-content-between mb-2">
            <div>Capacité:</div>
            <div class="fw-bold">${bus.capacity} sièges</div>
          </div>
          <div class="d-flex justify-content-between">
            <div>Disposition:</div>
            <div class="fw-bold">${bus.layout_details.rows} rangées × ${bus.layout_details.columns} colonnes</div>
          </div>
        </div>
      </div>

      <h5 class="mb-3">Commodités</h5>
      <div class="card">
        <div class="card-body">
          <div class="row">
            ${bus.amenities.map((amenity) => `
              <div class="col-md-6 mb-2">
                <div class="d-flex align-items-center">
                  <div class="me-2">
                    <i class="fas ${formatUtils.getAmenityIcon(amenity.amenity_name)} text-primary"></i>
                  </div>
                  <div>
                    <div class="fw-bold">${amenity.amenity_name}</div>
                    <div class="small text-muted">${amenity.description}</div>
                  </div>
                </div>
              </div>
            `).join('')}
          </div>
        </div>
      </div>
    </div>
  `;
}

/**
 * Renders pricing information
 * @param {Object} pricing - Pricing object
 * @returns {string} - HTML for pricing
 */
function renderPricing(pricing) {
  return `
    <div class="col-md-6">
      <h5 class="mb-3">Tarifs par type de siège</h5>
      <div class="card mb-3">
        <div class="card-body">
          <table class="pricing-table">
            <thead>
              <tr>
                <th>Type de siège</th>
                <th>Prix</th>
              </tr>
            </thead>
            <tbody>
              ${Object.entries(pricing).map(([type, price]) => `
                <tr>
                  <td><span class="seat-type-badge ${type}">${type === "standard" ? "Standard" : "Premium"}</span></td>
                  <td class="fw-bold">${formatUtils.formatPrice(price)}</td>
                </tr>
              `).join('')}
            </tbody>
          </table>
        </div>
      </div>
    </div>
  `;
}

/**
 * Renders all trip details
 * @param {Object} data - Trip details data
 * @returns {string} - HTML for trip details
 */
function renderAllDetails(data) {
  const { trip, route, bus } = data;

  if (!trip || !route || !bus) {
    return '<div class="alert alert-danger">Des informations sont manquantes pour afficher les détails.</div>';
  }

  return `
    <div class="details-content">
      <!-- Ajouter une croix de fermeture -->
      <div class="text-end mb-2">
        <button class="btn btn-sm btn-close" onclick="this.closest('.trip-details').style.display = 'none'"></button>
      </div>

      <!-- Itinéraire -->
      <div class="tab-content active" id="itinerary-content">
        <div class="row">
          <div class="col-md-6">
            <h5 class="mb-3">Points d'arrêt</h5>
            <div class="stops-timeline">
              ${renderStops(route.stops)}
            </div>
          </div>

          <div class="col-md-6">
            <h5 class="mb-3">Détails du trajet</h5>
            <div class="card mb-3">
              <div class="card-body">
                <div class="d-flex justify-content-between mb-2">
                  <div>Distance totale:</div>
                  <div class="fw-bold">${route.distance} km</div>
                </div>
                <div class="d-flex justify-content-between mb-2">
                  <div>Durée estimée:</div>
                  <div class="fw-bold">${formatUtils.formatDuration(route.duration)}</div>
                </div>
                <div class="d-flex justify-content-between mb-2">
                  <div>Départ estimé:</div>
                  <div class="fw-bold">${formatUtils.formatFullDateTime(trip.estimated_departure_time)}</div>
                </div>
                <div class="d-flex justify-content-between mb-2">
                  <div>Arrivée estimée:</div>
                  <div class="fw-bold">${formatUtils.formatFullDateTime(trip.estimated_arrival_time)}</div>
                </div>
                <div class="d-flex justify-content-between">
                  <div>Statut:</div>
                  ${getStatusBadge(trip.status)}
                </div>
              </div>
            </div>

            ${trip.tracking_link ? `
              <a href="${trip.tracking_link}" target="_blank" class="btn btn-outline-primary w-100 mb-3">
                <i class="fas fa-location-arrow me-2"></i> Suivre ce trajet en temps réel
              </a>
            ` : ''}

            <div class="map-container text-center">
              <div>
                <i class="fas fa-map-marked-alt fa-3x text-muted mb-2"></i>
                <p class="text-muted">Carte d'itinéraire</p>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Bus -->
      <div class="tab-content" id="bus-content">
        <div class="row">
          ${renderBusInfo(bus)}

          <div class="col-md-6">
            <h5 class="mb-3">Photos du bus</h5>
            ${bus.bus_photos.length > 0 ? bus.bus_photos.map(photo => `
              <img src="${photo}" alt="Photo du bus - ${bus.brand} ${bus.model}" class="bus-photo mb-3">
            `).join('') : `
              <div class="text-center p-4 bg-light rounded">
                <i class="fas fa-bus fa-3x text-muted mb-2"></i>
                <p class="text-muted">Aucune photo disponible</p>
              </div>
            `}
          </div>
        </div>
      </div>
      <!-- Tarifs -->
      <div class="tab-content" id="pricing-content">
        <div class="row">
          ${renderPricing(trip.pricing)}
          <div class="col-md-6">
            <h5 class="mb-3">Disponibilité</h5>
            <div class="card">
              <div class="card-body">
                <div class="row">
                  ${Object.entries(bus.available_seats_count).map(([type, count]) => `
                    <div class="col-6 mb-3">
                      <div class="text-center">
                        <h6>${type === "standard" ? "Standard" : "Premium"}</h6>
                        <div class="fs-1 fw-bold ${count > 0 ? "text-success" : "text-danger"}">${count}</div>
                        <div class="small text-muted">sièges disponibles</div>
                      </div>
                    </div>
                  `).join('')}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  `;
}

/**
 * Shows active tab content
 * @param {HTMLElement} tab - Tab element
 * @param {HTMLElement} detailsElement - Details container
 * @param {string} tabName - Tab name
 */
function showActiveTab(tab, detailsElement, tabName) {
  const tabs = detailsElement.parentElement.querySelectorAll('.details-tab');
  tabs.forEach(t => t.classList.remove('active'));
  
  tab.classList.add('active');
  
  const allTabContents = detailsElement.querySelectorAll('.tab-content');
  allTabContents.forEach(content => content.classList.remove('active'));
  
  const activeTabContent = detailsElement.querySelector(`#${tabName}-content`);
  if (activeTabContent) {
    activeTabContent.classList.add('active');
  } else {
    console.warn(`Aucun contenu trouvé pour l'onglet ${tabName}`);
  }
}

/**
 * Fetches and renders trip details
 * @param {number} tripId - Trip ID
 * @param {HTMLElement} detailsElement - Details container
 * @param {string} tabName - Tab name
 * @returns {Promise<void>}
 */
export async function fetchDetailsAndRender(tripId, detailsElement, tabName) {
  try {
    const data = await fetchTripDetails(tripId);

    detailsElement.innerHTML = renderAllDetails(data);
    detailsElement.dataset.loaded = "true";

    const tab = detailsElement.parentElement.querySelector(`[data-tab="${tabName}"]`);
    showActiveTab(tab, detailsElement, tabName);
  } catch (error) {
    detailsElement.innerHTML = `<div class="alert alert-danger m-3">Erreur: ${error.message}</div>`;
  }
}

/**
 * Sets up trip details event listeners
 */
export function setupTripDetails() {
  document.addEventListener("click", function (e) {
    const tab = e.target.closest(".details-tab");
    if (!tab) return;

    const tripId = tab.dataset.tripId;
    const tabName = tab.dataset.tab;
    const detailsElement = document.getElementById(`details-${tripId}`);
    const wasActive = tab.classList.contains("active");

    document.querySelectorAll(".trip-details").forEach((d) => {
      if (d !== detailsElement) d.style.display = "none";
    });

    const bookBtn = detailsElement.closest(".trip-card").querySelector(".book-btn");
    bookBtn.textContent = "Choisir un siège";
    bookBtn.dataset.mode = "show-seats";

    detailsElement.dataset.mode = "tabs";

    if (!detailsElement.dataset.loaded) {
      fetchDetailsAndRender(tripId, detailsElement, tabName);
    } else {
      if (wasActive) {
        detailsElement.style.display = "none";
        tab.classList.remove("active");
      } else {
        detailsElement.style.display = "block";
        showActiveTab(tab, detailsElement, tabName);
      }
    }
  });
}
