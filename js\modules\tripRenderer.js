/**
 * Module for rendering trips
 */
import { formatUtils } from './formatUtils.js';
import { fetchTrips } from './api.js';

/**
 * Gets HTML for bus type badge
 * @param {string} busType - Bus type
 * @returns {string} - HTML for badge
 */
function getBusTypeBadge(busType) {
  return busType === "standard" ? "Standard" : "VIP";
}

/**
 * Gets HTML for seat availability badge
 * @param {boolean} isSoldOut - Whether the trip is sold out
 * @returns {string} - HTML for badge
 */
function getSeatAvailabilityBadge(isSoldOut) {
  return isSoldOut
    ? `<span class="sold-out-badge">
        <i class="fas fa-times-circle me-1"></i> Complet
      </span>`
    : `<span class="seat-availability">
        <i class="fas fa-users me-1"></i>
        Sièges disponibles
      </span>`;
}

/**
 * Renders amenities for a trip
 * @param {Array} amenities - List of amenities
 * @returns {string} - HTML for amenities
 */
function renderAmenities(amenities) {
  return amenities.slice(0, 4).map(amenity => `
    <span class="amenity-badge">
      <i class="fas ${formatUtils.getAmenityIcon(amenity.amenity_name)}"></i>
      ${amenity.amenity_name}
    </span>
  `).join("") + 
  (amenities.length > 4 ? `
    <span class="amenity-badge">
      <i class="fas fa-plus-circle"></i>
      ${amenities.length - 4} plus
    </span>` : "");
}

/**
 * Renders trips to the DOM
 * @param {Array} trips - List of trips to render
 * @param {HTMLElement} container - Container element
 */
function renderTripsToDOM(trips, container) {
  if (!trips) {
    container.innerHTML = `
      <div class="col-12">
        <div class="alert alert-danger">
          Impossible de charger les résultats. Veuillez réessayer.
        </div>
      </div>
    `;
    return;
  }

  if (trips.length === 0) {
    container.innerHTML = `
      <div class="col-12">
        <div class="alert alert-danger">
          Aucun trajet disponible pour ces critères de recherche.
        </div>
      </div>
    `;
    return;
  }

  let tripsHtml = "";
  trips.forEach(trip => {
    const departureTime = formatUtils.formatDateTime(trip.departure_time);
    const arrivalTime = formatUtils.formatDateTime(trip.arrival_time);
    const duration = formatUtils.formatDuration(trip.duration);
    const busType = trip.bus_type;
    const isSoldOut = trip.available_seats_count.standard + trip.available_seats_count.premium === 0;
    const lowestPrice = Math.min(...Object.values(trip.pricing).map(Number));

    const busTypeBadge = getBusTypeBadge(busType);
    const seatAvailabilityBadge = getSeatAvailabilityBadge(isSoldOut);
    const amenitiesHtml = renderAmenities(trip.bus_amenities);

    tripsHtml += `
      <div class="col-lg-12">
        <div class="card trip-card ${isSoldOut ? "sold-out" : ""}" data-duration="${trip.duration}">
          <div class="card-header d-flex justify-content-between align-items-center">
            <div>
              <h5 class="m-0 d-flex align-items-center">
                <i class="fa-solid fa-bus"></i> 
                <span class="ms-2">${trip.bus_brand} ${trip.bus_model}</span>
              </h5>
              <small>${trip.bus_registration_number}</small>
            </div>
            <div class="bus-type-${busType}">
              ${busTypeBadge}
            </div>
          </div>
          <div class="card-body">
            <div class="row">
              <div class="col-md-8">
                <div class="time-container">
                  <div class="text-center">
                    <div class="fs-4 fw-bold">${departureTime}</div>
                    <div class="text-muted">${trip.departure_location}</div>
                  </div>
                  <div class="departure-icon"></div>
                  <div class="route-line"></div>
                  <div class="arrival-icon"></div>
                  <div class="text-center">
                    <div class="fs-4 fw-bold">${arrivalTime}</div>
                    <div class="text-muted">${trip.arrival_location}</div>
                  </div>
                </div>
                <div class="mt-3">
                  ${amenitiesHtml}
                </div>
              </div>
              <div class="col-md-4">
                <div class="price-container">
                  <div class="d-flex justify-content-between align-items-center mb-2">
                    <div class="price">${formatUtils.formatPrice(lowestPrice)} FCFA</div>
                    ${seatAvailabilityBadge}
                  </div>
                  <button class="btn btn-primary book-btn w-100" ${isSoldOut ? "disabled" : ""} data-trip-id="${trip.trip_id}">
                    ${isSoldOut ? "Complet" : "Choisir un siège"}
                  </button>
                </div>
              </div>
            </div>
          </div>
          <div class="details-tabs d-flex p-3 border-top">
            <div class="details-tab" data-trip-id="${trip.trip_id}" data-tab="itinerary">
              <i class="fas fa-route me-2"></i>Itinéraire
            </div>
            <div class="details-tab" data-trip-id="${trip.trip_id}" data-tab="bus">
              <i class="fas fa-bus me-2"></i>Bus
            </div>
            <div class="details-tab" data-trip-id="${trip.trip_id}" data-tab="pricing">
              <i class="fas fa-tag me-2"></i>Tarifs
            </div>
          </div>
          <div class="trip-details" id="details-${trip.trip_id}"></div>
        </div>
      </div>
    `;
  });

  container.innerHTML = tripsHtml;
}

/**
 * Renders trips
 * @param {Array} trips - List of trips to render (optional)
 * @returns {Promise<Array>} - The rendered trips
 */
export async function renderTrips(trips) {
  const tripResultsContainer = document.getElementById("trip-results");

  if (!trips) {
    trips = await fetchTrips(
      new URLSearchParams(window.location.search).get("date"),
      new URLSearchParams(window.location.search).get("from"),
      new URLSearchParams(window.location.search).get("to")
    );
  }

  renderTripsToDOM(trips, tripResultsContainer);
  
  return trips;
}
