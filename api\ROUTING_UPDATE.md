# Mise à jour du routage - Bus Booking API

## Changements apportés

L'API Bus Booking System a été déplacée dans le sous-dossier `bus-booking/api`. Les modifications suivantes ont été apportées pour adapter le routage :

### Modifications dans `index.php`

1. **Parsing amélioré de l'URI** : Le système détecte et ignore automatiquement les segments de base path (`bus-booking` et `api`) avant de traiter la version de l'API.

2. **Nouvelle logique de routage** :
   - Filtrage des segments vides dans l'URL
   - Suppression automatique des segments de base path
   - Utilisation de `$api_uri` au lieu de `$uri` pour le routage

### Structure des URLs supportées

L'API supporte maintenant les formats d'URL suivants :

#### Format complet (avec base path)
```
http://localhost/bus-booking/api/v1/users/register
http://localhost/bus-booking/api/v1/routes
http://localhost/bus-booking/api/v1/trips/123/stops
```

#### Format direct (sans base path - pour compatibilité)
```
http://localhost/v1/users/register
http://localhost/v1/routes
http://localhost/v1/trips/123/stops
```

### Endpoints disponibles

- **Users** : `/v1/users/register`, `/v1/users/login`
- **Routes** : `/v1/routes`, `/v1/routes/{id}`
- **Locations** : `/v1/locations`
- **Trips** : `/v1/trips`, `/v1/trips/{id}`, `/v1/trips/{id}/stops`
- **Seats** : `/v1/seats`
- **Amenities** : `/v1/amenities`
- **Bookings** : `/v1/bookings`, `/v1/bookings/{id}`
- **Payments** : `/v1/payments`

### Configuration

La version de l'API est définie dans le fichier `.env` :
```
API_VERSION=v1
```

### Fichiers modifiés

- `api/index.php` : Logique de routage mise à jour
- `api/ROUTING_UPDATE.md` : Cette documentation

### Tests recommandés

Après ces modifications, il est recommandé de tester :

1. Les URLs avec le chemin complet (`/bus-booking/api/v1/...`)
2. Les URLs sans le chemin de base (`/v1/...`)
3. Tous les endpoints existants
4. La gestion des erreurs (URLs invalides, versions incorrectes)

### Notes importantes

- Le fichier `.htaccess` n'a pas été modifié car il fonctionne correctement avec la nouvelle logique
- La compatibilité descendante est maintenue
- Aucune modification n'est requise dans les contrôleurs ou modèles
