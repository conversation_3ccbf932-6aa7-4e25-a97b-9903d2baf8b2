<?php
require_once __DIR__ . '/../Models/UserModel.php';
require_once __DIR__ . '/../Helpers/response.php';
require_once __DIR__ . '/../Helpers/validate.php';
require_once __DIR__ . '/../Helpers/jwt.php';

class UserController {
    private $userModel;

    public function __construct() {
        $this->userModel = new UserModel();
    }

    /**
     * Inscription d'un nouvel utilisateur
     * @param array $params Données de la requête
     */
    public function register($params = []) {
        try {
            // Définir les règles de validation
            $validationRules = [
                'email' => 'required|email',
                'phone' => 'required',
                'password' => 'required',
                'first_name' => 'required',
                'last_name' => 'required'
            ];

            // Validation des données avec validateFields
            validateFields($params, $validationRules);

            // Vérification des doublons
            if($this->userModel->emailExists($params['email'])) {
                sendResponse(409, ['message' => 'Cet email est déjà utilisé']);
                return;
            }

            if($this->userModel->phoneExists($params['phone'])) {
                sendResponse(409, ['message' => 'Ce numéro de téléphone est déjà utilisé']);
                return;
            }

            $user_id = $this->userModel->createUser($params);

            // Récupérer les données de l'utilisateur créé
            $user = $this->userModel->getUserById($user_id);

            sendResponse(201, [
                'message' => 'Compte créé avec succès',
                'user_id' => $user_id,
                'user' => [
                    'id' => $user['user_id'],
                    'email' => $user['email'],
                    'first_name' => $user['first_name'],
                    'last_name' => $user['last_name']
                ]
            ]);

        } catch (Exception $e) {
            sendResponse(500, ['message' => 'Erreur lors de la création du compte', 'error' => $e->getMessage()]);
        }
    }

    /**
     * Authentification des utilisateurs
     * @param array $params Données de la requête
     */
    public function login($params = []) {
        try {
            $validationRules = [
                'email' => 'required|email',
                'password' => 'required',
            ];

            validateFields($params, $validationRules);

            $user = $this->userModel->findByEmailOrPhone($params['email']);

            if (!$user) {
                sendResponse(401, ['message' => 'Email ou mot de passe incorrect']);
                return;
            }

            if (!password_verify($params['password'], $user['password_hash'])) {
                sendResponse(401, ['message' => 'Email ou mot de passe incorrect']);
                return;
            }

            // Récupérer les rôles de l'utilisateur
            $roles = $this->userModel->getUserRole($user['user_id']);
            $authorizedRoles = array_column($roles, 'role_type');
            $requestRole = $params['role'] ?? 'traveler';

            if (!in_array($requestRole, $authorizedRoles)) {
                $requestRole = $authorizedRoles[0] ?? 'traveler'; // Utiliser le premier rôle disponible
            }

            // Générer le token JWT
            $token = generateJWT($user['user_id'], $requestRole);

            // Mettre à jour la dernière connexion
            $this->userModel->updateLastLogin($user['user_id']);

            sendResponse(200, [
                'message' => 'Connexion réussie',
                'token' => $token,
                'user' => [
                    'id' => $user['user_id'],
                    'email' => $user['email'],
                    'first_name' => $user['first_name'],
                    'last_name' => $user['last_name'],
                    'role' => $requestRole,
                    'roles' => $authorizedRoles
                ]
            ]);

        } catch (Exception $e) {
            sendResponse(500, ['message' => 'Erreur lors de la connexion', 'error' => $e->getMessage()]);
        }
    }

    /**
     * Récupère tous les utilisateurs (operator)
     * @return array Liste des utilisateurs
     */
    public function getAllUsers(): array {
        // Vérification des droits
        AuthMiddleware::authenticate('operator');

        $limit = $_GET['limit'] ?? 10;
        $page = $_GET['page'] ?? 1;
        $offset = ($page - 1) * $limit;

        return $this->userModel->getAllUsers($limit, $offset);
    }

    /**
     * Récupère un utilisateur spécifique
     * @param int $userId ID de l'utilisateur
     * @return array Données utilisateur
     */
    public function getUser(int $userId): array {
        // Vérification des droits
        $authData = AuthMiddleware::authenticate();
        if($authData['sub'] != $userId && $authData['role'] != 'operator') {
            http_response_code(403);
            return ['error' => 'Accès non autorisé'];
        }

        $user = $this->userModel->getUserById($userId);
        if(!$user) {
            http_response_code(404);
            return ['error' => 'Utilisateur non trouvé'];
        }

        return $user;
    }

    /**
     * Met à jour un utilisateur
     * @param int $userId ID de l'utilisateur
     * @param array $request Données à mettre à jour
     * @return array Réponse JSON
     */
    public function updateUser(int $userId, array $request): array {
        // Vérification des droits
        $authData = AuthMiddleware::authenticate();
        if($authData['sub'] != $userId && $authData['role'] != 'operator') {
            http_response_code(403);
            return ['error' => 'Accès non autorisé'];
        }

        $success = $this->userModel->updateUser($userId, $request);
        if(!$success) {
            http_response_code(400);
            return ['error' => 'Aucune donnée valide à mettre à jour'];
        }

        return ['message' => 'Utilisateur mis à jour avec succès'];
    }

    /**
     * Supprime un utilisateur
     * @param int $userId ID de l'utilisateur
     * @return array Réponse JSON
     */
    public function deleteUser(int $userId): array {
        AuthMiddleware::authenticate('operator');

        $success = $this->userModel->deleteUser($userId);
        if(!$success) {
            http_response_code(404);
            return ['error' => 'Utilisateur non trouvé'];
        }

        return ['message' => 'Utilisateur supprimé avec succès'];
    }

    /**
     * Ajoute un rôle à un utilisateur (operator)
     * @param int $userId ID de l'utilisateur
     * @param array $request Données de la requête
     * @return array Réponse JSON
     */
    public function addUserRole(int $userId, array $request): array {
        AuthMiddleware::authenticate('operator');

        if(empty($request['role_type'])) {
            http_response_code(400);
            return ['error' => 'Le champ role_type est obligatoire'];
        }

        $success = $this->userModel->addUserRole(
            $userId, 
            $request['role_type'], 
            $_SERVER['AUTH_USER_ID'] // ID récupéré du token JWT
        );

        if(!$success) {
            http_response_code(500);
            return ['error' => 'Échec de l\'ajout du rôle'];
        }

        return ['message' => 'Rôle ajouté avec succès'];
    }

    /**
     * Supprime un rôle d'un utilisateur (operator)
     * @param int $userId ID de l'utilisateur
     * @param string $roleType Type de rôle
     * @return array Réponse JSON
     */
    public function removeUserRole(int $userId, string $roleType): array {
        AuthMiddleware::authenticate('operator');

        $success = $this->userModel->removeUserRole($userId, $roleType);
        if(!$success) {
            http_response_code(404);
            return ['error' => 'Rôle non trouvé'];
        }

        return ['message' => 'Rôle supprimé avec succès'];
    }

    /**
     * Obtenir le profil de l'utilisateur connecté
     * GET /v1/users/profile
     */
    public function getProfile($params = []) {
        try {
            $user = AuthMiddleware::authenticate();

            $userProfile = $this->userModel->getUserById($user->sub);

            if (!$userProfile) {
                sendResponse(404, ['message' => 'Utilisateur non trouvé']);
                return;
            }

            // Supprimer les données sensibles
            unset($userProfile['password_hash']);

            sendResponse(200, ['user' => $userProfile]);

        } catch (Exception $e) {
            sendResponse(500, ['message' => 'Erreur serveur', 'error' => $e->getMessage()]);
        }
    }

    /**
     * Mettre à jour le profil de l'utilisateur connecté
     * PUT /v1/users/profile
     */
    public function updateProfile($params = []) {
        try {
            $user = AuthMiddleware::authenticate();

            $allowedFields = ['first_name', 'last_name', 'phone', 'date_of_birth', 'gender'];
            $updateData = [];

            foreach ($allowedFields as $field) {
                if (isset($params[$field])) {
                    $updateData[$field] = $params[$field];
                }
            }

            if (empty($updateData)) {
                sendResponse(400, ['message' => 'Aucune donnée à mettre à jour']);
                return;
            }

            $success = $this->userModel->updateUser($user->sub, $updateData);

            if ($success) {
                sendResponse(200, ['message' => 'Profil mis à jour avec succès']);
            } else {
                sendResponse(500, ['message' => 'Erreur lors de la mise à jour']);
            }

        } catch (Exception $e) {
            sendResponse(500, ['message' => 'Erreur serveur', 'error' => $e->getMessage()]);
        }
    }

    /**
     * Obtenir les données du tableau de bord utilisateur
     * GET /v1/users/dashboard
     */
    public function getDashboard($params = []) {
        try {
            $user = AuthMiddleware::authenticate();

            // Récupérer les statistiques de l'utilisateur
            $stats = [
                'total_bookings' => $this->userModel->getUserBookingCount($user->sub),
                'upcoming_trips' => $this->userModel->getUserUpcomingTrips($user->sub),
                'completed_trips' => $this->userModel->getUserCompletedTrips($user->sub),
                'total_spent' => $this->userModel->getUserTotalSpent($user->sub)
            ];

            // Récupérer les réservations récentes
            $recentBookings = $this->userModel->getUserRecentBookings($user->sub, 5);

            // Récupérer les prochains voyages
            $upcomingTrips = $this->userModel->getUserUpcomingTripDetails($user->sub, 3);

            sendResponse(200, [
                'stats' => $stats,
                'recent_bookings' => $recentBookings,
                'upcoming_trips' => $upcomingTrips
            ]);

        } catch (Exception $e) {
            sendResponse(500, ['message' => 'Erreur serveur', 'error' => $e->getMessage()]);
        }
    }

    /**
     * Déconnexion de l'utilisateur
     * POST /v1/auth/logout
     */
    public function logout($params = []) {
        try {
            // Dans une implémentation complète, on pourrait blacklister le token
            // Pour l'instant, on renvoie juste une confirmation
            sendResponse(200, ['message' => 'Déconnexion réussie']);

        } catch (Exception $e) {
            sendResponse(500, ['message' => 'Erreur serveur', 'error' => $e->getMessage()]);
        }
    }

    /**
     * Rafraîchir le token JWT
     * POST /v1/auth/refresh
     */
    public function refreshToken($params = []) {
        try {
            $user = AuthMiddleware::authenticate();

            // Générer un nouveau token
            $newToken = generateJWT($user->sub, $user->role);

            sendResponse(200, [
                'message' => 'Token rafraîchi',
                'token' => $newToken
            ]);

        } catch (Exception $e) {
            sendResponse(500, ['message' => 'Erreur serveur', 'error' => $e->getMessage()]);
        }
    }
}
