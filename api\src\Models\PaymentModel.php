<?php
require_once __DIR__ . '/../Helpers/db.php';

class PaymentModel {
    private $db;

    public function __construct() {
        $this->db = getDB();
    }

    public function createPayment($data) {
        $sql = "INSERT INTO payment (booking_id, amount, currency, payment_method, payment_provider,
                                   payment_status, created_by)
                VALUES (:booking_id, :amount, :currency, :payment_method, :payment_provider,
                        :payment_status, :created_by)";
        $stmt = $this->db->prepare($sql);
        $stmt->execute([
            ':booking_id' => $data['booking_id'],
            ':amount' => $data['amount'],
            ':currency' => $data['currency'],
            ':payment_method' => $data['payment_method'],
            ':payment_provider' => $data['payment_provider'] ?? null,
            ':payment_status' => $data['payment_status'] ?? 'pending',
            ':created_by' => $data['created_by'] ?? null
        ]);
        return $this->db->lastInsertId();
    }

    /**
     * Récupérer un paiement par son ID
     */
    public function getPaymentById($paymentId) {
        $sql = "SELECT p.*, b.booking_id, b.total_amount as booking_amount
                FROM payment p
                JOIN booking b ON p.booking_id = b.booking_id
                WHERE p.payment_id = :payment_id";

        $stmt = $this->db->prepare($sql);
        $stmt->execute([':payment_id' => $paymentId]);
        return $stmt->fetch(PDO::FETCH_ASSOC);
    }

    /**
     * Récupérer un paiement par l'ID de transaction du fournisseur
     */
    public function getPaymentByTransactionId($transactionId) {
        $sql = "SELECT * FROM payment WHERE provider_transaction_id = :transaction_id";
        $stmt = $this->db->prepare($sql);
        $stmt->execute([':transaction_id' => $transactionId]);
        return $stmt->fetch(PDO::FETCH_ASSOC);
    }

    /**
     * Mettre à jour un paiement
     */
    public function updatePayment($paymentId, $data) {
        $allowedFields = ['payment_reference', 'provider_transaction_id', 'payment_status'];
        $updateFields = [];
        $params = [':payment_id' => $paymentId];

        foreach ($allowedFields as $field) {
            if (isset($data[$field])) {
                $updateFields[] = "{$field} = :{$field}";
                $params[":{$field}"] = $data[$field];
            }
        }

        if (empty($updateFields)) {
            return false;
        }

        $sql = "UPDATE payment SET " . implode(', ', $updateFields) . " WHERE payment_id = :payment_id";
        $stmt = $this->db->prepare($sql);
        return $stmt->execute($params);
    }

    /**
     * Mettre à jour le statut d'un paiement
     */
    public function updatePaymentStatus($paymentId, $status) {
        $sql = "UPDATE payment SET payment_status = :status, updated_at = NOW() WHERE payment_id = :payment_id";
        $stmt = $this->db->prepare($sql);
        return $stmt->execute([
            ':payment_id' => $paymentId,
            ':status' => $status
        ]);
    }

    /**
     * Créer un remboursement
     */
    public function createRefund($data) {
        $sql = "INSERT INTO refund (payment_id, amount, refund_reference, refund_status, processed_by)
                VALUES (:payment_id, :amount, :refund_reference, 'processed', :processed_by)";

        $stmt = $this->db->prepare($sql);
        return $stmt->execute([
            ':payment_id' => $data['payment_id'],
            ':amount' => $data['amount'],
            ':refund_reference' => $data['refund_reference'],
            ':processed_by' => $data['processed_by']
        ]);
    }

    /**
     * Obtenir les statistiques de revenus
     */
    public function getTotalRevenue() {
        $sql = "SELECT SUM(amount) as total FROM payment WHERE payment_status = 'successful'";
        $stmt = $this->db->prepare($sql);
        $stmt->execute();
        $result = $stmt->fetch(PDO::FETCH_ASSOC);
        return $result['total'] ?? 0;
    }

    public function getTodayRevenue() {
        $sql = "SELECT SUM(amount) as total FROM payment
                WHERE payment_status = 'successful' AND DATE(created_at) = CURDATE()";
        $stmt = $this->db->prepare($sql);
        $stmt->execute();
        $result = $stmt->fetch(PDO::FETCH_ASSOC);
        return $result['total'] ?? 0;
    }

    public function getMonthRevenue() {
        $sql = "SELECT SUM(amount) as total FROM payment
                WHERE payment_status = 'successful'
                AND YEAR(created_at) = YEAR(CURDATE())
                AND MONTH(created_at) = MONTH(CURDATE())";
        $stmt = $this->db->prepare($sql);
        $stmt->execute();
        $result = $stmt->fetch(PDO::FETCH_ASSOC);
        return $result['total'] ?? 0;
    }

    public function getLastMonthRevenue() {
        $sql = "SELECT SUM(amount) as total FROM payment
                WHERE payment_status = 'successful'
                AND YEAR(created_at) = YEAR(CURDATE() - INTERVAL 1 MONTH)
                AND MONTH(created_at) = MONTH(CURDATE() - INTERVAL 1 MONTH)";
        $stmt = $this->db->prepare($sql);
        $stmt->execute();
        $result = $stmt->fetch(PDO::FETCH_ASSOC);
        return $result['total'] ?? 0;
    }

    /**
     * Obtenir les paiements en attente
     */
    public function getPendingPayments() {
        $sql = "SELECT p.*, b.booking_id FROM payment p
                JOIN booking b ON p.booking_id = b.booking_id
                WHERE p.payment_status = 'pending'
                ORDER BY p.created_at DESC";

        $stmt = $this->db->prepare($sql);
        $stmt->execute();
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }

    /**
     * Obtenir les tendances de revenus
     */
    public function getRevenueTrends($days = 30) {
        $sql = "SELECT DATE(created_at) as date, SUM(amount) as revenue
                FROM payment
                WHERE payment_status = 'successful'
                AND created_at >= DATE_SUB(CURDATE(), INTERVAL :days DAY)
                GROUP BY DATE(created_at)
                ORDER BY date ASC";

        $stmt = $this->db->prepare($sql);
        $stmt->execute([':days' => $days]);
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }

    /**
     * Rapport de revenus
     */
    public function getRevenueReport($period, $startDate, $endDate) {
        $groupBy = '';
        $dateFormat = '';

        switch ($period) {
            case 'day':
                $groupBy = 'DATE(created_at)';
                $dateFormat = '%Y-%m-%d';
                break;
            case 'week':
                $groupBy = 'YEARWEEK(created_at)';
                $dateFormat = '%Y-W%u';
                break;
            case 'month':
                $groupBy = 'YEAR(created_at), MONTH(created_at)';
                $dateFormat = '%Y-%m';
                break;
            default:
                $groupBy = 'DATE(created_at)';
                $dateFormat = '%Y-%m-%d';
        }

        $sql = "SELECT DATE_FORMAT(created_at, '{$dateFormat}') as period,
                       SUM(amount) as revenue,
                       COUNT(*) as transaction_count
                FROM payment
                WHERE payment_status = 'successful'";

        $params = [];

        if ($startDate) {
            $sql .= " AND created_at >= :start_date";
            $params[':start_date'] = $startDate;
        }

        if ($endDate) {
            $sql .= " AND created_at <= :end_date";
            $params[':end_date'] = $endDate;
        }

        $sql .= " GROUP BY {$groupBy} ORDER BY created_at ASC";

        $stmt = $this->db->prepare($sql);
        $stmt->execute($params);
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }
}