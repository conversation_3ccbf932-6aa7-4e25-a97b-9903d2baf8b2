/* ==========================================================================
         VARIABLES ET STYLES GÉNÉRAUX
         ========================================================================== */
      :root {
        --primary-color: #0d6efd;
        --secondary-color: #f8f9fa;
        --accent-color: #ffc107;
        --dark-color: #212529;
      }
      
      body {
        font-family: "Segoe UI", Tahoma, Geneva, Verdana, sans-serif;
        background-color: #f5f5f5;
      }
      
      /* ==========================================================================
         FORMULAIRE DE RECHERCHE ET DROPDOWN VILLE
         ========================================================================== */
      
      /* Search form container */
      .search-form-container {
        margin-top: -50px;
        z-index: 10;
      }
      
      /* Search form */
      .search-form {
        background-color: white;
        border-radius: 15px;
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
        padding: 30px;
      }
      
      .search-form .form-control {
        border-radius: 8px;
        padding: 12px 15px;
        font-size: 1rem;
        border: 1px solid #dee2e6;
      }
      
      .search-form .form-control:focus {
        box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.15);
        border-color: var(--primary-color);
      }
      
      .search-btn {
        border-radius: 8px;
        padding: 12px 25px;
        font-weight: 600;
        font-size: 1rem;
        background-color: var(--primary-color);
        border-color: var(--primary-color);
        transition: all 0.3s;
      }
      
      .search-btn:hover {
        transform: translateY(-2px);
        box-shadow: 0 5px 15px rgba(13, 110, 253, 0.3);
      }
      
      /* Dropdown pour les villes */
      .city-dropdown {
        position: absolute;
        z-index: 1000;
        width: 100%;
        max-height: 300px;
        overflow-y: auto;
        background-color: white;
        border-radius: 8px;
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
        display: none;
      }
      
      .city-dropdown.show {
        display: block;
      }
      
      .city-item {
        padding: 12px 15px;
        border-bottom: 1px solid #f0f0f0;
        cursor: pointer;
        transition: background-color 0.2s;
      }
      
      .city-item:hover {
        background-color: #f8f9fa;
      }
      
      .city-item:last-child {
        border-bottom: none;
      }
      
      .city-name {
        font-weight: 600;
        font-size: 1rem;
      }
      
      .city-details {
        font-size: 0.8rem;
        color: #6c757d;
      }
      
      /* ==========================================================================
         BANNIÈRE DE RECHERCHE
         ========================================================================== */
      .search-banner {
        background: linear-gradient(rgba(0, 0, 0, 0.5), rgba(0, 0, 0, 0.5)),
                    url("/img/easybus.webp") center/cover no-repeat;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        background-size: cover;
        background-position: center;
        height: 200px;
      }
      
      /* ==========================================================================
         FOOTER
         ========================================================================== */
      footer {
        background-color: var(--dark-color);
        color: white;
        padding: 60px 0 30px;
      }
      
      .footer-title {
        font-size: 1.2rem;
        font-weight: 600;
        margin-bottom: 20px;
        padding-bottom: 10px;
        border-bottom: 2px solid rgba(255, 255, 255, 0.1);
      }
      
      .footer-links {
        list-style: none;
        padding: 0;
        margin: 0;
      }
      
      .footer-links li {
        margin-bottom: 10px;
      }
      
      .footer-links a {
        color: rgba(255, 255, 255, 0.7);
        text-decoration: none;
        transition: color 0.2s;
      }
      
      .footer-links a:hover {
        color: white;
      }
      
      .social-links {
        margin-top: 20px;
      }
      
      .social-links a {
        color: white;
        background-color: rgba(255, 255, 255, 0.1);
        width: 36px;
        height: 36px;
        border-radius: 50%;
        display: inline-flex;
        align-items: center;
        justify-content: center;
        margin-right: 10px;
        transition: background-color 0.2s;
      }
      
      .social-links a:hover {
        background-color: var(--primary-color);
      }
      
      .footer-bottom {
        border-top: 1px solid rgba(255, 255, 255, 0.1);
        padding-top: 20px;
        margin-top: 40px;
      }
      
      /* ==========================================================================
         CARTES DE TRAJET (PAGE RÉSULTAT DE RECHERCHE)
         ========================================================================== */
      .trip-card {
        border-radius: 12px;
        overflow: hidden;
        transition: transform 0.3s, box-shadow 0.3s;
        margin-bottom: 20px;
        border: none;
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
      }
      
      .trip-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
      }
      
      .card-header {
        background: linear-gradient(135deg, var(--primary-color), #0052cc);
        color: white;
        border-bottom: none;
        padding: 15px 20px;
      }
      
      /* Types de bus */
      .bus-type-standard {
        background-color: #e9f2ff;
        color: var(--primary-color);
        font-weight: 600;
        padding: 5px 10px;
        border-radius: 20px;
        font-size: 0.8rem;
        display: inline-block;
      }
      
      .bus-type-vip {
        background-color: #fff8e1;
        color: #ff9800;
        font-weight: 600;
        padding: 5px 10px;
        border-radius: 20px;
        font-size: 0.8rem;
        display: inline-block;
      }
      
      /* Lignes et icônes temporelles */
      .route-line {
        position: relative;
        height: 2px;
        background-color: #dee2e6;
        margin: 0 10px;
        flex-grow: 1;
      }
      
      .time-container {
        display: flex;
        align-items: center;
        margin: 20px 0;
      }
      
      .departure-icon,
      .arrival-icon {
        width: 12px;
        height: 12px;
        background-color: var(--primary-color);
        border-radius: 50%;
        margin: 0 5px;
      }
      
      /* Informations et badges */
      .amenity-badge {
        background-color: var(--secondary-color);
        color: var(--dark-color);
        border-radius: 20px;
        padding: 5px 12px;
        margin-right: 8px;
        margin-bottom: 8px;
        display: inline-block;
        font-size: 0.85rem;
      }
      
      .amenity-badge i {
        margin-right: 5px;
        color: var(--primary-color);
      }
      
      .price-container {
        background-color: #f8f9fa;
        border-radius: 8px;
        padding: 15px;
      }
      
      .price {
        font-size: 1.5rem;
        font-weight: 700;
        color: var(--primary-color);
      }
      
      .book-btn {
        border-radius: 50px;
        padding: 8px 25px;
        font-weight: 600;
        transition: all 0.3s;
      }
      
      .book-btn:hover {
        transform: scale(1.05);
      }
      
      .sold-out {
        opacity: 0.7;
      }
      
      .duration-badge {
        background-color: rgba(13, 110, 253, 0.1);
        color: var(--primary-color);
        padding: 5px 10px;
        border-radius: 20px;
        font-weight: 600;
        font-size: 0.85rem;
      }
      
      .seat-availability {
        font-size: 0.9rem;
        color: #198754;
        font-weight: 600;
      }
      
      .sold-out-badge {
        background-color: #f8d7da;
        color: #dc3545;
        padding: 5px 10px;
        border-radius: 20px;
        font-weight: 600;
        font-size: 0.85rem;
      }
      
      .trip-date {
        color: #6c757d;
        font-size: 0.9rem;
      }
      
      /* Entête de recherche pour résultats */
      .search-header {
        background-color: white;
        border-radius: 12px;
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
        padding: 20px;
        margin-bottom: 30px;
      }
      
      /* ==========================================================================
         DÉTAILS DU TRAJET & ONGLETS
         ========================================================================== */
      .trip-details {
        display: none;
        padding: 15px;
        background-color: #f8f9fa;
        border-top: 1px solid #e9ecef;
        max-height: 0;
        overflow: hidden;
        transition: max-height 0.3s ease-out;
      }
      
      .trip-details[data-loaded="true"] {
        display: block; /* Afficher quand chargé */
        max-height: 2000px; /* Valeur supérieure à la hauteur maximale attendue */
      }
      
      .details-tab {
        cursor: pointer;
        padding: 10px 15px;
        background-color: #f8f9fa;
        border-radius: 5px;
        margin-right: 10px;
        transition: all 0.3s;
        color: #6c757d;
        font-weight: 600;
      }
      
      .details-tab.active {
        background-color: var(--primary-color);
        color: white;
      }
      
      .details-tab.active::after {
        content: "▼";
        margin-left: 8px;
        font-size: 0.8em;
        transition: transform 0.3s;
      }
      
      .details-content {
        padding: 20px 0;
      }
      
      .tab-content {
        display: none;
      }
      
      .tab-content.active {
        display: block;
      }
      
      /* Pour la flèche sur onglet en cas de cache */
      .trip-details[style*="display: none"] + .details-tabs .details-tab.active::after {
        transform: rotate(180deg);
      }
      
      /* Timeline des arrêts */
      .stops-timeline {
        position: relative;
        padding-left: 30px;
      }
      
      .stops-timeline::before {
        content: "";
        position: absolute;
        left: 10px;
        top: 0;
        bottom: 0;
        width: 2px;
        background-color: var(--primary-color);
      }
      
      .timeline-stop {
        position: relative;
        margin-bottom: 20px;
        padding-bottom: 15px;
      }
      
      .timeline-stop::before {
        content: "";
        position: absolute;
        left: -30px;
        top: 5px;
        width: 20px;
        height: 20px;
        border-radius: 50%;
        background-color: white;
        border: 2px solid var(--primary-color);
        z-index: 1;
      }
      
      .timeline-stop.boarding::before {
        background-color: #28a745;
        border-color: #28a745;
      }
      
      .timeline-stop.dropping::before {
        background-color: #dc3545;
        border-color: #dc3545;
      }

      .map-container {
        height: 200px;
        border-radius: 8px;
        overflow: hidden;
        margin-bottom: 20px;
        background-color: #e9ecef;
        display: flex;
        align-items: center;
        justify-content: center;
      }
      
      /* ==========================================================================
         SEAT MAP & BUS LAYOUT (SIÈGES, BUS, ET INFORMATIONS)
         ========================================================================== */
      
      .seat-instruction {
        background-color: #e6e6fa;
        color: #4b0082;
        padding: 10px;
        border-radius: 5px;
        text-align: center;
        margin-bottom: 20px;
        font-size: 0.9rem;
      }
      
      /* Légende */
      .seat-legend {
        display: flex;
        gap: 10px;
        margin-bottom: 20px;
        justify-content: center;
      }
      
      .legend-item {
        display: flex;
        align-items: center;
        gap: 10px;
        font-size: 0.9rem;
      }
      
      .legend-item .seat {
        margin: 0;
      }
      
      /* ==========================================================================
         BUS CONTAINER & PLAN
         ========================================================================== */
      .bus-container {
        position: relative;
        border-radius: 15px;
        padding: 20px;
        background-color: #ffffff;
        margin-top: 20px;
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1),
                    0 8px 16px rgba(0, 0, 0, 0.15);
        transition: transform 0.3s ease-in-out;
      }
      
      .bus-plan-container {
        display: flex;
        flex-direction: column;
        align-items: center;
      }
      
      .bus-layout {
        display: grid;
        gap: 10px;
      }
      
      .seat svg {
        width: 25px;
        height: 25px;
      }

      .seat.available svg path {
        fill: #6c757d;
      }

      .seat.occupied svg path {
        fill: #f8b6bc;
      }

      .seat.selected svg path {
        fill: #0d6efd;
      }

      .weekend-icon {
        transform: scaleY(1.3);
      }
      
      .seat.empty {
        background: none;
        border: none;
      }

      /* Container des détails de siège */
.chosen-seats-details { 
  display: grid;
  grid-template-columns: auto auto auto; /* Les colonnes s'adaptent à la taille du contenu */
  gap: 10px; /* Espace entre les colonnes */
  text-align: right; /* Aligne le texte à droite */
  margin-bottom: 10px;
  justify-content: end;
}

.chosen-seat-detail {
  padding: 5px;
  font-size: 14px; /* Ajustez la taille de la police à votre goût */
}


      
      /* ==========================================================================
         FORMULAIRE DES POINTS D'ARRÊT
         ========================================================================== */
      .stop-form-container {
        padding: 20px;
        border-left: 1px solid #dee2e6;
      }
      
      .stop-form-container h5 {
        font-size: 1.1rem;
        font-weight: 600;
        margin-bottom: 15px;
        border-bottom: 2px solid #dc3545;
        padding-bottom: 5px;
      }
      
      .stop-form-container .form-check {
        margin-bottom: 10px;
      }
      
      .stop-form-container .form-check-label {
        font-size: 0.9rem;
      }
      
      .stop-form-container .stop-time {
        font-weight: 600;
        margin-right: 10px;
      }
      
      .stop-form-container .stop-details {
        color: #6c757d;
        font-size: 0.8rem;
      }
      
      .total-amount {
        text-align: right;
        font-size: 1.2rem;
        font-weight: 700;
        color: #dc3545;
        margin-top: 20px;
      }
      
      .total-amount small {
        display: block;
        font-size: 0.8rem;
        color: #6c757d;
        font-weight: normal;
      }
      
      /* ==========================================================================
         INDICATEURS ET FORMULAIRES MULTI-ÉTAPES
         ========================================================================== */
      .step-indicator {
        display: flex;
        justify-content: space-between;
        margin-bottom: 20px;
      }
      
      .step {
        flex: 1;
        text-align: center;
        font-size: 0.9rem;
        color: #6c757d;
        position: relative;
      }
      
      .step.active {
        color: #007bff;
      }
      
      .step::after {
        content: "";
        position: absolute;
        top: 50%;
        left: 50%;
        width: 100%;
        height: 2px;
        background-color: #dee2e6;
        z-index: -1;
      }
      
      .step:first-child::after {
        left: 0;
      }
      
      .step:last-child::after {
        display: none;
      }
      
      .step.active::after {
        background-color: #007bff;
      }
      
      .form-step {
        display: none;
      }
      
      .form-step.active {
        display: block;
      }
      
      .passenger-details,
      .billing-details {
        margin-bottom: 15px;
        padding: 15px;
        border-radius: 8px;
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1),
                    0 8px 16px rgba(0, 0, 0, 0.15);
      }
      
      .passenger-details h6,
      .billing-details h6 {
        margin-bottom: 10px;
      }
      
      .review-details p {
        margin-bottom: 5px;
      }
	  
	/* ==========================================================================
		RÈGLES SPÉCIFIQUES POUR MOBILE
	========================================================================== */
	@media (max-width: 768px) {
	  /* Ajustements généraux */
	  .hero-heading {
		font-size: 2rem;
	  }
	  
	  .hero-subheading {
		font-size: 1.2rem;
	  }
	  
	  .search-form-container {
		margin-top: -75px;
	  }
	  
	  .route-card {
		flex-direction: column;
	  }
	  
	  .route-image {
		width: 100%;
		height: 150px;
	  }
	  
	  .hero-section {
		height: 30vh;
	  }
	  
	  /* Ajustement des blocs tarif & onglets */
	  .price-container {
		margin-top: 15px;
	  }
	  
	  .details-tabs {
		overflow-x: auto;
		white-space: nowrap;
		padding-bottom: 10px;
	  }

	  /* Ajustements pour le formulaire des points d'arrêt */
	  .stop-form-container {
		border-left: none;
		border-top: 1px solid #dee2e6;
		padding-top: 20px;
	  }
	}

/* ==========================================================================
   STYLES POUR LES NOUVELLES FONCTIONNALITÉS
   ========================================================================== */

/* Variables CSS */
:root {
  --primary-color: #0d6efd;
  --secondary-color: #f8f9fa;
  --accent-color: #ffc107;
  --dark-color: #212529;
}

/* Hero section styles complets */
.hero-section {
  height: 60vh;
  display: flex;
  align-items: center;
  color: white;
  position: relative;
  overflow: hidden;
}

.hero-section::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(rgba(0, 0, 0, 0.5), rgba(0, 0, 0, 0.5)),
    url("/img/easybus.webp") center/cover no-repeat;
  z-index: -1;
}

.hero-content {
  text-align: center;
  max-width: 800px;
  margin: 0 auto;
  padding: 0 20px;
  z-index: 1;
}

.hero-heading {
  font-size: 3rem;
  font-weight: 700;
  margin-bottom: 1rem;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
}

.hero-subheading {
  font-size: 1.5rem;
  margin-bottom: 2rem;
  text-shadow: 1px 1px 3px rgba(0, 0, 0, 0.5);
}

/* Main content sections */
.section-title {
  font-size: 2.2rem;
  font-weight: 700;
  text-align: center;
  margin-bottom: 3rem;
  position: relative;
  padding-bottom: 15px;
}

.section-title:after {
  content: "";
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 80px;
  height: 3px;
  background-color: var(--primary-color);
}

.feature-card {
  background-color: white;
  border-radius: 12px;
  padding: 30px;
  text-align: center;
  margin-bottom: 30px;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
  transition: transform 0.3s, box-shadow 0.3s;
}

.feature-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 15px 30px rgba(0, 0, 0, 0.1);
}

.feature-icon {
  width: 70px;
  height: 70px;
  border-radius: 50%;
  background-color: rgba(13, 110, 253, 0.1);
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 20px;
  color: var(--primary-color);
  font-size: 1.8rem;
}

.feature-title {
  font-size: 1.3rem;
  font-weight: 600;
  margin-bottom: 15px;
}

.route-card {
  display: flex;
  background-color: white;
  border-radius: 12px;
  overflow: hidden;
  margin-bottom: 20px;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
  transition: transform 0.3s, box-shadow 0.3s;
}

.route-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 15px 30px rgba(0, 0, 0, 0.1);
}

.route-image {
  width: 150px;
  background-color: #ccc;
}

.route-content {
  padding: 20px;
  flex-grow: 1;
}

.route-price {
  font-size: 1.5rem;
  font-weight: 700;
  color: var(--primary-color);
}

.testimonial-card {
  background-color: white;
  border-radius: 12px;
  padding: 30px;
  margin-bottom: 30px;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
  transition: transform 0.3s, box-shadow 0.3s;
}

.testimonial-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 15px 30px rgba(0, 0, 0, 0.1);
}

.testimonial-avatar {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  margin-right: 15px;
  background-color: #ccc;
}

.testimonial-rating {
  color: var(--accent-color);
  margin-bottom: 15px;
}

/* Footer styles */
footer {
  background-color: var(--dark-color);
  color: white;
  padding: 60px 0 30px;
}

.footer-title {
  font-size: 1.2rem;
  font-weight: 600;
  margin-bottom: 20px;
  padding-bottom: 10px;
  border-bottom: 2px solid rgba(255, 255, 255, 0.1);
}

.footer-links {
  list-style: none;
  padding: 0;
  margin: 0;
}

.footer-links li {
  margin-bottom: 10px;
}

.footer-links a {
  color: rgba(255, 255, 255, 0.7);
  text-decoration: none;
  transition: color 0.2s;
}

.footer-links a:hover {
  color: white;
}

.social-links {
  margin-top: 20px;
}

.social-links a {
  color: white;
  background-color: rgba(255, 255, 255, 0.1);
  width: 36px;
  height: 36px;
  border-radius: 50%;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  margin-right: 10px;
  transition: background-color 0.2s;
}

.social-links a:hover {
  background-color: var(--primary-color);
}

.footer-bottom {
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  padding-top: 20px;
  margin-top: 40px;
}