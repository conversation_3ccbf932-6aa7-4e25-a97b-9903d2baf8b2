<?php
require_once __DIR__ . '/../Helpers/db.php';

class UserModel {
    private $db;

    public function __construct() {
        $this->db = getDB();
    }

    /**
     * Crée un nouvel utilisateur
     * @param array $userData Données utilisateur
     * @return int ID de l'utilisateur créé
     * @throws PDOException
     */
    public function createUser(array $userData): int {
        $sql = "INSERT INTO user (email, phone, password_hash, first_name, last_name) 
                VALUES (:email, :phone, :password_hash, :first_name, :last_name)";

        $stmt = $this->db->prepare($sql);
        $stmt->execute([
            ':email' => $userData['email'],
            ':phone' => $userData['phone'],
            ':password_hash' => password_hash($userData['password'], PASSWORD_DEFAULT),
            ':first_name' => $userData['first_name'],
            ':last_name' => $userData['last_name']
        ]);

        return $this->db->lastInsertId();
    }

    public function getUserByEmail($email) {
        $sql = "SELECT * FROM user WHERE email = :email";
        $stmt = $this->db->prepare($sql);
        $stmt->execute([':email' => $email]);
        return $stmt->fetch();
    }
    
    /**
     * Trouve un utilisateur par email ou téléphone
     * @param string $identifier Email ou numéro de téléphone
     * @return array|null Données utilisateur ou null
     */
    public function findByEmailOrPhone(string $identifier): ?array {
        $sql = "SELECT * FROM user WHERE email = ? OR phone = ?";
        $stmt = $this->db->prepare($sql);
        $stmt->execute([$identifier, $identifier]);
        return $stmt->fetch(PDO::FETCH_ASSOC) ?: null;
    }

    /**
     * Récupère tous les utilisateurs (pour admin)
     * @param int $limit Limite de résultats
     * @param int $offset Offset de pagination
     * @return array Liste des utilisateurs
     */
    public function getAllUsers(int $limit = 10, int $offset = 0): array {
        $stmt = $this->db->prepare("SELECT * FROM user LIMIT :limit OFFSET :offset");
        $stmt->bindValue(':limit', $limit, PDO::PARAM_INT);
        $stmt->bindValue(':offset', $offset, PDO::PARAM_INT);
        $stmt->execute();
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }

    /**
     * Récupère un utilisateur par ID
     * @param int $userId ID de l'utilisateur
     * @return array|null Données utilisateur ou null
     */
    public function getUserById(int $userId): ?array {
        $stmt = $this->db->prepare("SELECT * FROM user WHERE user_id = ?");
        $stmt->execute([$userId]);
        return $stmt->fetch(PDO::FETCH_ASSOC) ?: null;
    }

    /**
     * Met à jour les informations utilisateur
     * @param int $userId ID de l'utilisateur
     * @param array $updateData Données à mettre à jour
     * @return bool Succès de l'opération
     */
    public function updateUser(int $userId, array $updateData): bool {
        $allowedFields = ['email', 'phone', 'first_name', 'last_name', 'date_of_birth', 'profile_picture'];
        $setParts = [];
        $params = [':user_id' => $userId];

        foreach ($updateData as $key => $value) {
            if(in_array($key, $allowedFields)) {
                $setParts[] = "$key = :$key";
                $params[":$key"] = $value;
            }
        }

        if(empty($setParts)) return false;

        $sql = "UPDATE user SET ".implode(', ', $setParts)." WHERE user_id = :user_id";
        $stmt = $this->db->prepare($sql);
        return $stmt->execute($params);
    }

    /**
     * Supprime un utilisateur
     * @param int $userId ID de l'utilisateur
     * @return bool Succès de l'opération
     */
    public function deleteUser(int $userId): bool {
        $stmt = $this->db->prepare("DELETE FROM user WHERE user_id = ?");
        return $stmt->execute([$userId]);
    }

    // --------------------------
    // Méthodes pour les rôles des utilisateurs
    // --------------------------

    /**
     * Récupérer la liste des rôles d'un utilisateur
     * @param int $userId ID de l'utilisateur
     * @return array Liste des rôles
     */
    public function getUserRole(int $user_id): array {
        $sql = "SELECT role_type FROM user_role 
                WHERE user_id = :user_id AND is_active = TRUE";
        $stmt = $this->db->prepare($sql);
        $stmt->bindParam(':user_id', $user_id, PDO::PARAM_INT);
        $stmt->execute();
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }

    /**
     * Ajoute un rôle à un utilisateur
     * @param int $userId ID de l'utilisateur
     * @param string $roleType Type de rôle
     * @param int $assignedBy ID de l'admin qui assigne
     * @return bool Succès de l'opération
     */
    public function addUserRole(int $userId, string $roleType, int $assignedBy): bool {
        $sql = "INSERT INTO user_role (user_id, role_type, assigned_by) 
                VALUES (?, ?, ?) 
                ON DUPLICATE KEY UPDATE is_active = TRUE";
        $stmt = $this->db->prepare($sql);
        return $stmt->execute([$userId, $roleType, $assignedBy]);
    }

    /**
     * Supprime un rôle d'un utilisateur
     * @param int $userId ID de l'utilisateur
     * @param string $roleType Type de rôle à supprimer
     * @return bool Succès de l'opération
     */
    public function removeUserRole(int $userId, string $roleType): bool {
        $stmt = $this->db->prepare("DELETE FROM user_role WHERE user_id = ? AND role_type = ?");
        return $stmt->execute([$userId, $roleType]);
    }

    // --------------------------
    // Méthodes de validation
    // --------------------------

    /**
     * Vérifie si l'email existe déjà
     * @param string $email Email à vérifier
     * @return bool Existe ou non
     */
    public function emailExists(string $email): bool {
        $stmt = $this->db->prepare("SELECT COUNT(*) FROM user WHERE email = ?");
        $stmt->execute([$email]);
        return $stmt->fetchColumn() > 0;
    }

    /**
     * Crée un utilisateur invité (sans mot de passe)
     * @param array $userData Données utilisateur
     * @return int ID de l'utilisateur créé
     */
    public function createGuestUser(array $userData): int {
        $sql = "INSERT INTO user (email, phone, password_hash, first_name, last_name, verification_status, status)
                VALUES (:email, :phone, :password_hash, :first_name, :last_name, :verification_status, :status)";

        $stmt = $this->db->prepare($sql);
        $stmt->execute([
            ':email' => $userData['email'],
            ':phone' => $userData['phone'],
            ':password_hash' => $userData['password_hash'], // null pour les invités
            ':first_name' => $userData['first_name'],
            ':last_name' => $userData['last_name'],
            ':verification_status' => $userData['verification_status'] ?? 'pending',
            ':status' => $userData['status'] ?? 'active'
        ]);

        $userId = $this->db->lastInsertId();

        // Ajouter le rôle de voyageur par défaut
        $this->addUserRole($userId, 'traveler', $userId);

        return $userId;
    }

    /**
     * Vérifie si le téléphone existe déjà
     * @param string $phone Téléphone à vérifier
     * @return bool Existe ou non
     */
    public function phoneExists(string $phone): bool {
        $stmt = $this->db->prepare("SELECT COUNT(*) FROM user WHERE phone = ?");
        $stmt->execute([$phone]);
        return $stmt->fetchColumn() > 0;
    }

    /**
     * Met à jour le statut de dernière connexion
     * @param int $userId ID de l'utilisateur
     * @return bool Succès de l'opération
     */
    public function updateLastLogin(int $userId): bool {
        $stmt = $this->db->prepare("UPDATE user SET last_login_at = NOW() WHERE user_id = ?");
        return $stmt->execute([$userId]);
    }

    /**
     * Obtenir les statistiques des utilisateurs
     * @return array Statistiques
     */
    public function getUserStats(): array {
        $sql = "SELECT
                    COUNT(*) as total_users,
                    SUM(CASE WHEN status = 'active' THEN 1 ELSE 0 END) as active_users,
                    SUM(CASE WHEN verification_status = 'verified' THEN 1 ELSE 0 END) as verified_users,
                    SUM(CASE WHEN password_hash IS NULL THEN 1 ELSE 0 END) as guest_users,
                    SUM(CASE WHEN last_login_at >= DATE_SUB(NOW(), INTERVAL 30 DAY) THEN 1 ELSE 0 END) as active_last_month
                FROM user";

        $stmt = $this->db->prepare($sql);
        $stmt->execute();
        return $stmt->fetch(PDO::FETCH_ASSOC);
    }

    /**
     * Obtenir le nombre de réservations d'un utilisateur
     */
    public function getUserBookingCount($userId) {
        $sql = "SELECT COUNT(*) as count FROM booking WHERE user_id = ?";
        $stmt = $this->db->prepare($sql);
        $stmt->execute([$userId]);
        $result = $stmt->fetch(PDO::FETCH_ASSOC);
        return $result['count'] ?? 0;
    }

    /**
     * Obtenir le nombre de voyages à venir d'un utilisateur
     */
    public function getUserUpcomingTrips($userId) {
        $sql = "SELECT COUNT(*) as count FROM booking b
                JOIN trip t ON b.trip_id = t.trip_id
                WHERE b.user_id = ? AND t.estimated_departure_time > NOW()
                AND b.booking_status = 'confirmed'";
        $stmt = $this->db->prepare($sql);
        $stmt->execute([$userId]);
        $result = $stmt->fetch(PDO::FETCH_ASSOC);
        return $result['count'] ?? 0;
    }

    /**
     * Obtenir le nombre de voyages terminés d'un utilisateur
     */
    public function getUserCompletedTrips($userId) {
        $sql = "SELECT COUNT(*) as count FROM booking b
                JOIN trip t ON b.trip_id = t.trip_id
                WHERE b.user_id = ? AND t.estimated_arrival_time < NOW()
                AND b.booking_status = 'confirmed'";
        $stmt = $this->db->prepare($sql);
        $stmt->execute([$userId]);
        $result = $stmt->fetch(PDO::FETCH_ASSOC);
        return $result['count'] ?? 0;
    }

    /**
     * Obtenir le montant total dépensé par un utilisateur
     */
    public function getUserTotalSpent($userId) {
        $sql = "SELECT SUM(p.amount) as total FROM payment p
                JOIN booking b ON p.booking_id = b.booking_id
                WHERE b.user_id = ? AND p.payment_status = 'successful'";
        $stmt = $this->db->prepare($sql);
        $stmt->execute([$userId]);
        $result = $stmt->fetch(PDO::FETCH_ASSOC);
        return $result['total'] ?? 0;
    }

    /**
     * Obtenir les réservations récentes d'un utilisateur
     */
    public function getUserRecentBookings($userId, $limit = 5) {
        $sql = "SELECT b.booking_id, b.total_amount, b.booking_status, b.created_at,
                       r.route_name, t.estimated_departure_time,
                       l1.location_name as departure_location,
                       l2.location_name as destination_location
                FROM booking b
                JOIN trip t ON b.trip_id = t.trip_id
                JOIN route r ON t.route_id = r.route_id
                JOIN location l1 ON r.departure_location_id = l1.location_id
                JOIN location l2 ON r.destination_location_id = l2.location_id
                WHERE b.user_id = ?
                ORDER BY b.created_at DESC
                LIMIT ?";

        $stmt = $this->db->prepare($sql);
        $stmt->execute([$userId, $limit]);
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }

    /**
     * Obtenir les détails des prochains voyages d'un utilisateur
     */
    public function getUserUpcomingTripDetails($userId, $limit = 3) {
        $sql = "SELECT b.booking_id, b.total_amount, t.estimated_departure_time, t.estimated_arrival_time,
                       r.route_name, l1.location_name as departure_location,
                       l2.location_name as destination_location,
                       st1.stop_name as boarding_stop, st2.stop_name as dropping_stop,
                       COUNT(tk.ticket_id) as ticket_count
                FROM booking b
                JOIN trip t ON b.trip_id = t.trip_id
                JOIN route r ON t.route_id = r.route_id
                JOIN location l1 ON r.departure_location_id = l1.location_id
                JOIN location l2 ON r.destination_location_id = l2.location_id
                JOIN stop st1 ON b.boarding_stop_id = st1.stop_id
                JOIN stop st2 ON b.dropping_stop_id = st2.stop_id
                LEFT JOIN ticket tk ON b.booking_id = tk.booking_id
                WHERE b.user_id = ? AND t.estimated_departure_time > NOW()
                AND b.booking_status = 'confirmed'
                GROUP BY b.booking_id
                ORDER BY t.estimated_departure_time ASC
                LIMIT ?";

        $stmt = $this->db->prepare($sql);
        $stmt->execute([$userId, $limit]);
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }

    /**
     * Obtenir les segments de clients pour les analyses
     */
    public function getCustomerSegments() {
        $sql = "SELECT
                    CASE
                        WHEN booking_count >= 10 THEN 'VIP'
                        WHEN booking_count >= 5 THEN 'Régulier'
                        WHEN booking_count >= 2 THEN 'Occasionnel'
                        ELSE 'Nouveau'
                    END as segment,
                    COUNT(*) as user_count,
                    AVG(total_spent) as avg_spent
                FROM (
                    SELECT u.user_id,
                           COUNT(b.booking_id) as booking_count,
                           COALESCE(SUM(p.amount), 0) as total_spent
                    FROM user u
                    LEFT JOIN booking b ON u.user_id = b.user_id
                    LEFT JOIN payment p ON b.booking_id = p.booking_id AND p.payment_status = 'successful'
                    WHERE u.password_hash IS NOT NULL
                    GROUP BY u.user_id
                ) user_stats
                GROUP BY segment
                ORDER BY avg_spent DESC";

        $stmt = $this->db->prepare($sql);
        $stmt->execute();
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }
}