<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Connexion / Inscription - EasyBus</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="css/style.css" rel="stylesheet">
    <style>
        .auth-container {
            min-height: 100vh;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .auth-card {
            background: white;
            border-radius: 15px;
            box-shadow: 0 15px 35px rgba(0,0,0,0.1);
            overflow: hidden;
            max-width: 900px;
            width: 100%;
        }
        .auth-tabs {
            background: #f8f9fa;
            border-bottom: 1px solid #dee2e6;
        }
        .auth-tabs .nav-link {
            border: none;
            color: #6c757d;
            font-weight: 600;
            padding: 1rem 2rem;
        }
        .auth-tabs .nav-link.active {
            background: white;
            color: #0d6efd;
            border-bottom: 3px solid #0d6efd;
        }
        .form-floating label {
            color: #6c757d;
        }
        .btn-auth {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            padding: 12px 30px;
            font-weight: 600;
            border-radius: 8px;
        }
        .btn-auth:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }
        .guest-booking {
            background: #e3f2fd;
            border: 1px solid #bbdefb;
            border-radius: 10px;
            padding: 20px;
            margin-top: 20px;
        }
        .guest-booking h5 {
            color: #1976d2;
            margin-bottom: 15px;
        }
    </style>
</head>
<body>
    <div class="auth-container">
        <div class="auth-card">
            <!-- Onglets -->
            <ul class="nav nav-tabs auth-tabs" id="authTabs" role="tablist">
                <li class="nav-item" role="presentation">
                    <button class="nav-link active" id="login-tab" data-bs-toggle="tab" data-bs-target="#login" type="button" role="tab">
                        <i class="fas fa-sign-in-alt me-2"></i>Connexion
                    </button>
                </li>
                <li class="nav-item" role="presentation">
                    <button class="nav-link" id="register-tab" data-bs-toggle="tab" data-bs-target="#register" type="button" role="tab">
                        <i class="fas fa-user-plus me-2"></i>Inscription
                    </button>
                </li>
            </ul>

            <div class="tab-content" id="authTabContent">
                <!-- Formulaire de connexion -->
                <div class="tab-pane fade show active" id="login" role="tabpanel">
                    <div class="p-4">
                        <div class="text-center mb-4">
                            <h3 class="fw-bold text-primary">Bon retour !</h3>
                            <p class="text-muted">Connectez-vous à votre compte EasyBus</p>
                        </div>

                        <form id="loginForm">
                            <div class="form-floating mb-3">
                                <input type="email" class="form-control" id="loginEmail" placeholder="Email" required>
                                <label for="loginEmail">Adresse email</label>
                            </div>

                            <div class="form-floating mb-3">
                                <input type="password" class="form-control" id="loginPassword" placeholder="Mot de passe" required>
                                <label for="loginPassword">Mot de passe</label>
                            </div>

                            <div class="d-flex justify-content-between align-items-center mb-3">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="rememberMe">
                                    <label class="form-check-label" for="rememberMe">
                                        Se souvenir de moi
                                    </label>
                                </div>
                                <a href="#" class="text-decoration-none">Mot de passe oublié ?</a>
                            </div>

                            <button type="submit" class="btn btn-auth btn-primary w-100 mb-3">
                                <i class="fas fa-sign-in-alt me-2"></i>Se connecter
                            </button>
                        </form>

                        <!-- Réservation sans compte -->
                        <div class="guest-booking">
                            <h5><i class="fas fa-user-clock me-2"></i>Réserver sans compte</h5>
                            <p class="mb-3">Vous pouvez également réserver sans créer de compte. Vous recevrez vos tickets par email.</p>
                            <a href="index.html" class="btn btn-outline-primary">
                                <i class="fas fa-ticket-alt me-2"></i>Réserver maintenant
                            </a>
                        </div>
                    </div>
                </div>

                <!-- Formulaire d'inscription -->
                <div class="tab-pane fade" id="register" role="tabpanel">
                    <div class="p-4">
                        <div class="text-center mb-4">
                            <h3 class="fw-bold text-primary">Créer un compte</h3>
                            <p class="text-muted">Rejoignez EasyBus pour une expérience personnalisée</p>
                        </div>

                        <form id="registerForm">
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="form-floating mb-3">
                                        <input type="text" class="form-control" id="firstName" placeholder="Prénom" required>
                                        <label for="firstName">Prénom</label>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-floating mb-3">
                                        <input type="text" class="form-control" id="lastName" placeholder="Nom" required>
                                        <label for="lastName">Nom de famille</label>
                                    </div>
                                </div>
                            </div>

                            <div class="form-floating mb-3">
                                <input type="email" class="form-control" id="registerEmail" placeholder="Email" required>
                                <label for="registerEmail">Adresse email</label>
                            </div>

                            <div class="form-floating mb-3">
                                <input type="tel" class="form-control" id="phone" placeholder="Téléphone" required>
                                <label for="phone">Numéro de téléphone</label>
                            </div>

                            <div class="form-floating mb-3">
                                <input type="password" class="form-control" id="registerPassword" placeholder="Mot de passe" required>
                                <label for="registerPassword">Mot de passe</label>
                            </div>

                            <div class="form-floating mb-3">
                                <input type="password" class="form-control" id="confirmPassword" placeholder="Confirmer le mot de passe" required>
                                <label for="confirmPassword">Confirmer le mot de passe</label>
                            </div>

                            <div class="form-check mb-3">
                                <input class="form-check-input" type="checkbox" id="acceptTerms" required>
                                <label class="form-check-label" for="acceptTerms">
                                    J'accepte les <a href="#" class="text-decoration-none">conditions d'utilisation</a> et la <a href="#" class="text-decoration-none">politique de confidentialité</a>
                                </label>
                            </div>

                            <button type="submit" class="btn btn-auth btn-primary w-100">
                                <i class="fas fa-user-plus me-2"></i>Créer mon compte
                            </button>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Messages d'alerte -->
    <div class="position-fixed top-0 end-0 p-3" style="z-index: 11">
        <div id="alertContainer"></div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="js/auth.js"></script>
</body>
</html>
