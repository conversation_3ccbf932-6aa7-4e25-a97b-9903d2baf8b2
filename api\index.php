<?php
// Démarrer la session et gérer les erreurs
error_reporting(E_ALL);
ini_set('display_errors', 1);
session_start();

// Charger l'autoloader de Composer
require_once __DIR__ . '/vendor/autoload.php';

// Charger les helpers et le routeur
require_once __DIR__ . '/src/Helpers/response.php';
require_once __DIR__ . '/src/Router/Router.php';

// Configuration CORS
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization, X-Requested-With');
header('Content-Type: application/json; charset=utf-8');

// Gérer les requêtes OPTIONS (preflight CORS)
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit;
}

// Charger les variables d'environnement
if (file_exists(__DIR__ . '/.env')) {
    $dotenv = Dotenv\Dotenv::createImmutable(__DIR__);
    $dotenv->load();
}

// Charger la configuration
$config = require_once __DIR__ . '/src/config/app.php';

// Gestion des erreurs globales
set_exception_handler(function($exception) {
    error_log('Exception non gérée: ' . $exception->getMessage());
    sendResponse(500, [
        'message' => 'Erreur interne du serveur',
        'error' => $_ENV['APP_DEBUG'] === 'true' ? $exception->getMessage() : 'Une erreur est survenue'
    ]);
});

try {
    // Initialiser et dispatcher le routeur
    $router = new Router();
    $router->dispatch();
    
} catch (Exception $e) {
    error_log('Erreur routeur: ' . $e->getMessage());
    sendResponse(500, [
        'message' => 'Erreur de routage',
        'error' => $_ENV['APP_DEBUG'] === 'true' ? $e->getMessage() : 'Erreur interne'
    ]);
}
?>
