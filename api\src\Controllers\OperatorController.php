<?php
require_once __DIR__ . '/../Models/BookingModel.php';
require_once __DIR__ . '/../Models/TripModel.php';
require_once __DIR__ . '/../Models/BusModel.php';
require_once __DIR__ . '/../Models/UserModel.php';
require_once __DIR__ . '/../Models/PaymentModel.php';
require_once __DIR__ . '/../Helpers/response.php';
require_once __DIR__ . '/../Middlewares/AuthMiddleware.php';

class OperatorController {
    private $bookingModel;
    private $tripModel;
    private $busModel;
    private $userModel;
    private $paymentModel;
    
    public function __construct() {
        $this->bookingModel = new BookingModel();
        $this->tripModel = new TripModel();
        $this->busModel = new BusModel();
        $this->userModel = new UserModel();
        $this->paymentModel = new PaymentModel();
    }
    
    /**
     * Tableau de bord principal de l'opérateur
     * GET /v1/operator/dashboard
     */
    public function getDashboard($params = []) {
        try {
            // Authentification requise (opérateur)
            $user = AuthMiddleware::authenticate('operator');
            
            // Statistiques générales
            $stats = [
                'bookings' => $this->getBookingStats(),
                'trips' => $this->getTripStats(),
                'revenue' => $this->getRevenueStats(),
                'buses' => $this->getBusStats(),
                'users' => $this->userModel->getUserStats()
            ];
            
            // Réservations récentes
            $recentBookings = $this->getRecentBookings(10);
            
            // Voyages du jour
            $todayTrips = $this->getTodayTrips();
            
            // Alertes et notifications
            $alerts = $this->getSystemAlerts();
            
            sendResponse(200, [
                'stats' => $stats,
                'recent_bookings' => $recentBookings,
                'today_trips' => $todayTrips,
                'alerts' => $alerts
            ]);
            
        } catch (Exception $e) {
            sendResponse(500, ['message' => 'Erreur serveur', 'error' => $e->getMessage()]);
        }
    }
    
    /**
     * Gestion des réservations
     * GET /v1/operator/bookings
     */
    public function getBookings($params = []) {
        try {
            $user = AuthMiddleware::authenticate('operator');
            
            $page = $params['page'] ?? $_GET['page'] ?? 1;
            $limit = $params['limit'] ?? $_GET['limit'] ?? 20;
            $status = $params['status'] ?? $_GET['status'] ?? '';
            $search = $params['search'] ?? $_GET['search'] ?? '';
            
            $bookings = $this->bookingModel->getBookingsForOperator($page, $limit, $status, $search);
            $total = $this->bookingModel->getBookingsCountForOperator($status, $search);
            
            sendResponse(200, [
                'bookings' => $bookings,
                'pagination' => [
                    'current_page' => $page,
                    'per_page' => $limit,
                    'total' => $total,
                    'total_pages' => ceil($total / $limit)
                ]
            ]);
            
        } catch (Exception $e) {
            sendResponse(500, ['message' => 'Erreur serveur', 'error' => $e->getMessage()]);
        }
    }
    
    /**
     * Rapports et analyses
     * GET /v1/operator/reports
     */
    public function getReports($params = []) {
        try {
            $user = AuthMiddleware::authenticate('operator');
            
            $type = $params['type'] ?? $_GET['type'] ?? 'revenue';
            $period = $params['period'] ?? $_GET['period'] ?? 'month';
            $startDate = $params['start_date'] ?? $_GET['start_date'] ?? '';
            $endDate = $params['end_date'] ?? $_GET['end_date'] ?? '';
            
            switch ($type) {
                case 'revenue':
                    $data = $this->getRevenueReport($period, $startDate, $endDate);
                    break;
                case 'bookings':
                    $data = $this->getBookingsReport($period, $startDate, $endDate);
                    break;
                case 'routes':
                    $data = $this->getRoutesReport($period, $startDate, $endDate);
                    break;
                case 'occupancy':
                    $data = $this->getOccupancyReport($period, $startDate, $endDate);
                    break;
                default:
                    sendResponse(400, ['message' => 'Type de rapport non valide']);
                    return;
            }
            
            sendResponse(200, [
                'type' => $type,
                'period' => $period,
                'data' => $data
            ]);
            
        } catch (Exception $e) {
            sendResponse(500, ['message' => 'Erreur serveur', 'error' => $e->getMessage()]);
        }
    }
    
    /**
     * Analyses et métriques
     * GET /v1/operator/analytics
     */
    public function getAnalytics($params = []) {
        try {
            $user = AuthMiddleware::authenticate('operator');
            
            $analytics = [
                'revenue_trends' => $this->getRevenueTrends(),
                'popular_routes' => $this->getPopularRoutes(),
                'peak_times' => $this->getPeakTimes(),
                'customer_segments' => $this->getCustomerSegments(),
                'bus_performance' => $this->getBusPerformance()
            ];
            
            sendResponse(200, ['analytics' => $analytics]);
            
        } catch (Exception $e) {
            sendResponse(500, ['message' => 'Erreur serveur', 'error' => $e->getMessage()]);
        }
    }
    
    // Méthodes privées pour les statistiques
    
    private function getBookingStats() {
        return [
            'total' => $this->bookingModel->getTotalBookings(),
            'today' => $this->bookingModel->getTodayBookings(),
            'pending' => $this->bookingModel->getPendingBookings(),
            'confirmed' => $this->bookingModel->getConfirmedBookings(),
            'cancelled' => $this->bookingModel->getCancelledBookings()
        ];
    }
    
    private function getTripStats() {
        return [
            'total' => $this->tripModel->getTotalTrips(),
            'today' => $this->tripModel->getTodayTrips(),
            'ongoing' => $this->tripModel->getOngoingTrips(),
            'completed' => $this->tripModel->getCompletedTrips(),
            'cancelled' => $this->tripModel->getCancelledTrips()
        ];
    }
    
    private function getRevenueStats() {
        return [
            'total' => $this->paymentModel->getTotalRevenue(),
            'today' => $this->paymentModel->getTodayRevenue(),
            'this_month' => $this->paymentModel->getMonthRevenue(),
            'last_month' => $this->paymentModel->getLastMonthRevenue()
        ];
    }
    
    private function getBusStats() {
        return [
            'total' => $this->busModel->getTotalBuses(),
            'active' => $this->busModel->getActiveBuses(),
            'maintenance' => $this->busModel->getBusesInMaintenance(),
            'utilization_rate' => $this->busModel->getBusUtilizationRate()
        ];
    }
    
    private function getRecentBookings($limit = 10) {
        return $this->bookingModel->getRecentBookingsForOperator($limit);
    }
    
    private function getTodayTrips() {
        return $this->tripModel->getTodayTripsForOperator();
    }
    
    private function getSystemAlerts() {
        $alerts = [];
        
        // Vérifier les bus en maintenance
        $maintenanceBuses = $this->busModel->getBusesInMaintenance();
        if (count($maintenanceBuses) > 0) {
            $alerts[] = [
                'type' => 'warning',
                'message' => count($maintenanceBuses) . ' bus(es) en maintenance',
                'action' => 'Vérifier les bus en maintenance'
            ];
        }
        
        // Vérifier les voyages en retard
        $delayedTrips = $this->tripModel->getDelayedTrips();
        if (count($delayedTrips) > 0) {
            $alerts[] = [
                'type' => 'danger',
                'message' => count($delayedTrips) . ' voyage(s) en retard',
                'action' => 'Gérer les retards'
            ];
        }
        
        // Vérifier les paiements en attente
        $pendingPayments = $this->paymentModel->getPendingPayments();
        if (count($pendingPayments) > 0) {
            $alerts[] = [
                'type' => 'info',
                'message' => count($pendingPayments) . ' paiement(s) en attente',
                'action' => 'Vérifier les paiements'
            ];
        }
        
        return $alerts;
    }
    
    private function getRevenueTrends() {
        return $this->paymentModel->getRevenueTrends(30); // 30 derniers jours
    }
    
    private function getPopularRoutes() {
        return $this->bookingModel->getPopularRoutes(10);
    }
    
    private function getPeakTimes() {
        return $this->bookingModel->getPeakBookingTimes();
    }
    
    private function getCustomerSegments() {
        return $this->userModel->getCustomerSegments();
    }
    
    private function getBusPerformance() {
        return $this->busModel->getBusPerformanceMetrics();
    }
    
    private function getRevenueReport($period, $startDate, $endDate) {
        return $this->paymentModel->getRevenueReport($period, $startDate, $endDate);
    }
    
    private function getBookingsReport($period, $startDate, $endDate) {
        return $this->bookingModel->getBookingsReport($period, $startDate, $endDate);
    }
    
    private function getRoutesReport($period, $startDate, $endDate) {
        return $this->tripModel->getRoutesReport($period, $startDate, $endDate);
    }
    
    private function getOccupancyReport($period, $startDate, $endDate) {
        return $this->tripModel->getOccupancyReport($period, $startDate, $endDate);
    }
}
