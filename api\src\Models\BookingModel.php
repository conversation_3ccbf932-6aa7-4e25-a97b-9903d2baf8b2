<?php
require_once __DIR__ . '/../Helpers/db.php';

class BookingModel {
    private $db;

    public function __construct() {
        $this->db = getDB();
    }

    // Lister les réservations d’un utilisateur
    public function getUserBookings($user_id) {
        $sql = "SELECT b.booking_id, t.trip_id, r.route_name, 
                       b.boarding_stop_id, b.dropping_stop_id, b.total_amount, b.booking_status 
                FROM booking b
                JOIN trip t ON b.trip_id = t.trip_id
                JOIN route r ON t.route_id = r.route_id
                WHERE b.user_id = :user_id";
        $stmt = $this->db->prepare($sql);
        $stmt->execute([':user_id' => $user_id]);
        return $stmt->fetchAll();
    }
    
    // Récupérer une réservation par ID
    public function getBookingById($booking_id, $user_id) {
        $sql = "SELECT b.booking_id, t.trip_id, r.route_name, 
                       s1.stop_name AS boarding_stop, s2.stop_name AS dropping_stop, 
                       b.total_amount, b.booking_status 
                FROM booking b
                JOIN trip t ON b.trip_id = t.trip_id
                JOIN route r ON t.route_id = r.route_id
                JOIN stop s1 ON b.boarding_stop_id = s1.stop_id
                JOIN stop s2 ON b.dropping_stop_id = s2.stop_id
                WHERE b.booking_id = :booking_id AND b.user_id = :user_id";
        $stmt = $this->db->prepare($sql);
        $stmt->execute([':booking_id' => $booking_id, ':user_id' => $user_id]);
        return $stmt->fetch();
    }
    
    // Marquer une réservation comme annulée
    public function cancelBooking($booking_id, $user_id) {
        $sql = "UPDATE booking SET booking_status = 'cancelled', cancelled_by = :user_id 
                WHERE booking_id = :booking_id AND user_id = :user_id";
        $stmt = $this->db->prepare($sql);
        $stmt->execute([':booking_id' => $booking_id, ':user_id' => $user_id]);
        return $stmt->rowCount();
    }

    // Créer une nouvelle réservation
    public function createBooking($data) {
        $sql = "INSERT INTO booking (user_id, trip_id, boarding_stop_id, dropping_stop_id, total_amount, created_by) 
                VALUES (:user_id, :trip_id, :boarding_stop_id, :dropping_stop_id, :total_amount, :created_by)";
        $stmt = $this->db->prepare($sql);
        $stmt->execute([
            ':user_id' => $data['user_id'],
            ':trip_id' => $data['trip_id'],
            ':boarding_stop_id' => $data['boarding_stop_id'],
            ':dropping_stop_id' => $data['dropping_stop_id'],
            ':total_amount' => $data['total_amount'],
            ':created_by' => $data['user_id']
        ]);
        return $this->db->lastInsertId();
    }
}