<?php
require_once __DIR__ . '/../Helpers/db.php';

class BookingModel {
    private $db;

    public function __construct() {
        $this->db = getDB();
    }

    // Lister les réservations d’un utilisateur
    public function getUserBookings($user_id) {
        $sql = "SELECT b.booking_id, t.trip_id, r.route_name, 
                       b.boarding_stop_id, b.dropping_stop_id, b.total_amount, b.booking_status 
                FROM booking b
                JOIN trip t ON b.trip_id = t.trip_id
                JOIN route r ON t.route_id = r.route_id
                WHERE b.user_id = :user_id";
        $stmt = $this->db->prepare($sql);
        $stmt->execute([':user_id' => $user_id]);
        return $stmt->fetchAll();
    }
    
    // Récupérer une réservation par ID
    public function getBookingById($booking_id, $user_id) {
        $sql = "SELECT b.booking_id, t.trip_id, r.route_name, 
                       s1.stop_name AS boarding_stop, s2.stop_name AS dropping_stop, 
                       b.total_amount, b.booking_status 
                FROM booking b
                JOIN trip t ON b.trip_id = t.trip_id
                JOIN route r ON t.route_id = r.route_id
                JOIN stop s1 ON b.boarding_stop_id = s1.stop_id
                JOIN stop s2 ON b.dropping_stop_id = s2.stop_id
                WHERE b.booking_id = :booking_id AND b.user_id = :user_id";
        $stmt = $this->db->prepare($sql);
        $stmt->execute([':booking_id' => $booking_id, ':user_id' => $user_id]);
        return $stmt->fetch();
    }
    
    // Marquer une réservation comme annulée
    public function cancelBooking($booking_id, $user_id) {
        $sql = "UPDATE booking SET booking_status = 'cancelled', cancelled_by = :user_id 
                WHERE booking_id = :booking_id AND user_id = :user_id";
        $stmt = $this->db->prepare($sql);
        $stmt->execute([':booking_id' => $booking_id, ':user_id' => $user_id]);
        return $stmt->rowCount();
    }

    // Créer une nouvelle réservation
    public function createBooking($data) {
        $sql = "INSERT INTO booking (user_id, trip_id, boarding_stop_id, dropping_stop_id, total_amount, created_by)
                VALUES (:user_id, :trip_id, :boarding_stop_id, :dropping_stop_id, :total_amount, :created_by)";
        $stmt = $this->db->prepare($sql);
        $stmt->execute([
            ':user_id' => $data['user_id'],
            ':trip_id' => $data['trip_id'],
            ':boarding_stop_id' => $data['boarding_stop_id'],
            ':dropping_stop_id' => $data['dropping_stop_id'],
            ':total_amount' => $data['total_amount'],
            ':created_by' => $data['user_id']
        ]);
        return $this->db->lastInsertId();
    }

    // Créer une réservation pour un invité
    public function createGuestBooking($data) {
        $sql = "INSERT INTO booking (user_id, trip_id, boarding_stop_id, dropping_stop_id, total_amount, booking_notes, created_by)
                VALUES (:user_id, :trip_id, :boarding_stop_id, :dropping_stop_id, :total_amount, :booking_notes, :created_by)";
        $stmt = $this->db->prepare($sql);
        $stmt->execute([
            ':user_id' => $data['user_id'],
            ':trip_id' => $data['trip_id'],
            ':boarding_stop_id' => $data['boarding_stop_id'],
            ':dropping_stop_id' => $data['dropping_stop_id'],
            ':total_amount' => $data['total_amount'],
            ':booking_notes' => 'Réservation invité - Email: ' . ($data['contact_email'] ?? '') . ' - Tél: ' . ($data['contact_phone'] ?? ''),
            ':created_by' => $data['user_id']
        ]);
        return $this->db->lastInsertId();
    }

    // Rechercher des réservations par email ou téléphone
    public function searchBookingsByContact($email, $phone) {
        $sql = "SELECT DISTINCT b.booking_id, b.trip_id, b.total_amount, b.booking_status, b.created_at,
                       r.route_name, t.estimated_departure_time, t.estimated_arrival_time,
                       l1.location_name as departure_location, l2.location_name as destination_location,
                       st1.stop_name as boarding_stop, st2.stop_name as dropping_stop
                FROM booking b
                JOIN trip t ON b.trip_id = t.trip_id
                JOIN route r ON t.route_id = r.route_id
                JOIN location l1 ON r.departure_location_id = l1.location_id
                JOIN location l2 ON r.destination_location_id = l2.location_id
                JOIN stop st1 ON b.boarding_stop_id = st1.stop_id
                JOIN stop st2 ON b.dropping_stop_id = st2.stop_id
                LEFT JOIN ticket tk ON b.booking_id = tk.booking_id
                WHERE (tk.passenger_email = :email OR tk.passenger_phone = :phone)
                   OR (b.booking_notes LIKE :email_search OR b.booking_notes LIKE :phone_search)
                ORDER BY b.created_at DESC";

        $stmt = $this->db->prepare($sql);
        $stmt->execute([
            ':email' => $email,
            ':phone' => $phone,
            ':email_search' => "%Email: {$email}%",
            ':phone_search' => "%Tél: {$phone}%"
        ]);
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }

    // Obtenir les détails d'une réservation pour l'email
    public function getBookingDetailsForEmail($bookingId) {
        $sql = "SELECT b.*, r.route_name, t.estimated_departure_time, t.estimated_arrival_time,
                       l1.location_name as departure_location, l2.location_name as destination_location,
                       st1.stop_name as boarding_stop, st2.stop_name as dropping_stop,
                       bus.brand as bus_brand, bus.model as bus_model, bus.registration_number,
                       u.first_name, u.last_name, u.email as passenger_email
                FROM booking b
                JOIN trip t ON b.trip_id = t.trip_id
                JOIN route r ON t.route_id = r.route_id
                JOIN location l1 ON r.departure_location_id = l1.location_id
                JOIN location l2 ON r.destination_location_id = l2.location_id
                JOIN stop st1 ON b.boarding_stop_id = st1.stop_id
                JOIN stop st2 ON b.dropping_stop_id = st2.stop_id
                JOIN bus ON t.bus_id = bus.bus_id
                JOIN user u ON b.user_id = u.user_id
                WHERE b.booking_id = :booking_id";

        $stmt = $this->db->prepare($sql);
        $stmt->execute([':booking_id' => $bookingId]);
        return $stmt->fetch(PDO::FETCH_ASSOC);
    }

    // Mettre à jour une réservation
    public function updateBooking($bookingId, $data, $userId) {
        $allowedFields = ['boarding_stop_id', 'dropping_stop_id', 'booking_notes'];
        $updateFields = [];
        $params = [':booking_id' => $bookingId, ':user_id' => $userId];

        foreach ($allowedFields as $field) {
            if (isset($data[$field])) {
                $updateFields[] = "{$field} = :{$field}";
                $params[":{$field}"] = $data[$field];
            }
        }

        if (empty($updateFields)) {
            return false;
        }

        $sql = "UPDATE booking SET " . implode(', ', $updateFields) . ", updated_by = :user_id
                WHERE booking_id = :booking_id AND user_id = :user_id";

        $stmt = $this->db->prepare($sql);
        return $stmt->execute($params);
    }
}