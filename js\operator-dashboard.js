// Configuration de l'API
const API_BASE_URL = 'api/v1';

// Variables globales
let dashboardData = {};
let revenueChart = null;
let currentSection = 'dashboard';

// Vérifier l'authentification au chargement de la page
document.addEventListener('DOMContentLoaded', function() {
    if (!isAuthenticated()) {
        window.location.href = 'auth.html?return=' + encodeURIComponent(window.location.pathname);
        return;
    }
    
    // Vérifier le rôle opérateur
    const user = getCurrentUser();
    if (!user || !user.roles || !user.roles.includes('operator')) {
        showAlert('Accès non autorisé. Vous devez être opérateur.', 'danger');
        setTimeout(() => {
            window.location.href = 'dashboard.html';
        }, 2000);
        return;
    }
    
    initializeOperatorDashboard();
});

// Initialiser le tableau de bord opérateur
async function initializeOperatorDashboard() {
    try {
        // Afficher les informations utilisateur
        displayUserInfo();
        
        // Charger les données du tableau de bord
        await loadDashboardData();
        
        // Gérer la validation des tickets
        setupTicketValidation();
        
    } catch (error) {
        console.error('Erreur lors de l\'initialisation:', error);
        showAlert('Erreur lors du chargement du tableau de bord', 'danger');
    }
}

// Afficher les informations utilisateur
function displayUserInfo() {
    const user = getCurrentUser();
    if (user) {
        document.getElementById('operatorName').textContent = user.first_name || 'Opérateur';
    }
}

// Charger les données du tableau de bord
async function loadDashboardData() {
    try {
        // Appel API pour récupérer les données du tableau de bord opérateur
        const response = await apiRequest('operator/dashboard');
        
        dashboardData = response;
        
        if (response.stats) {
            displayStats(response.stats);
        }
        
        if (response.today_trips) {
            displayTodayTrips(response.today_trips);
        }
        
        if (response.alerts) {
            displayAlerts(response.alerts);
        }
        
        // Charger le graphique des revenus
        await loadRevenueChart();
        
    } catch (error) {
        console.error('Erreur lors du chargement des données:', error);
        showAlert('Erreur lors du chargement des données', 'danger');
    }
}

// Afficher les statistiques
function displayStats(stats) {
    document.getElementById('totalBookingsToday').textContent = stats.bookings?.today || 0;
    document.getElementById('activeTrips').textContent = stats.trips?.ongoing || 0;
    document.getElementById('revenueToday').textContent = formatCurrency(stats.revenue?.today || 0);
    
    // Calculer le nombre de passagers (approximation basée sur les réservations)
    const avgPassengersPerBooking = 1.5; // Moyenne estimée
    const totalPassengers = Math.round((stats.bookings?.today || 0) * avgPassengersPerBooking);
    document.getElementById('totalPassengers').textContent = totalPassengers;
}

// Afficher les voyages du jour
function displayTodayTrips(trips) {
    const container = document.getElementById('todayTripsContainer');
    
    if (!trips || trips.length === 0) {
        container.innerHTML = `
            <div class="text-center py-4">
                <i class="fas fa-calendar-times fa-2x text-muted mb-3"></i>
                <p class="text-muted">Aucun voyage prévu aujourd'hui</p>
            </div>
        `;
        return;
    }
    
    let html = '<div class="table-responsive"><table class="table table-hover">';
    html += `
        <thead>
            <tr>
                <th>Trajet</th>
                <th>Heure départ</th>
                <th>Bus</th>
                <th>Réservations</th>
                <th>Statut</th>
                <th>Actions</th>
            </tr>
        </thead>
        <tbody>
    `;
    
    trips.forEach(trip => {
        const occupancyRate = trip.total_seats > 0 ? Math.round((trip.booked_seats / trip.total_seats) * 100) : 0;
        const statusClass = trip.status === 'ongoing' ? 'success' : trip.status === 'completed' ? 'secondary' : 'primary';
        
        html += `
            <tr>
                <td>
                    <strong>${trip.route_name}</strong><br>
                    <small class="text-muted">${trip.departure_location} → ${trip.destination_location}</small>
                </td>
                <td>${formatTime(trip.estimated_departure_time)}</td>
                <td>${trip.bus_registration || 'N/A'}</td>
                <td>
                    ${trip.booked_seats}/${trip.total_seats}
                    <div class="progress mt-1" style="height: 4px;">
                        <div class="progress-bar" style="width: ${occupancyRate}%"></div>
                    </div>
                </td>
                <td><span class="badge bg-${statusClass}">${getStatusText(trip.status)}</span></td>
                <td>
                    <button class="btn btn-sm btn-outline-primary" onclick="viewTripDetails(${trip.trip_id})">
                        <i class="fas fa-eye"></i>
                    </button>
                </td>
            </tr>
        `;
    });
    
    html += '</tbody></table></div>';
    container.innerHTML = html;
}

// Afficher les alertes
function displayAlerts(alerts) {
    const container = document.getElementById('alertsContainer');
    
    if (!alerts || alerts.length === 0) {
        container.innerHTML = `
            <div class="text-center py-4">
                <i class="fas fa-check-circle fa-2x text-success mb-3"></i>
                <p class="text-muted">Aucune alerte</p>
            </div>
        `;
        return;
    }
    
    let html = '';
    alerts.forEach(alert => {
        const iconClass = alert.type === 'danger' ? 'exclamation-triangle' : 
                         alert.type === 'warning' ? 'exclamation-circle' : 'info-circle';
        
        html += `
            <div class="alert-item border-start border-${alert.type} border-3">
                <div class="d-flex align-items-start">
                    <i class="fas fa-${iconClass} text-${alert.type} me-2 mt-1"></i>
                    <div class="flex-grow-1">
                        <div class="fw-bold">${alert.message}</div>
                        ${alert.action ? `<small class="text-muted">${alert.action}</small>` : ''}
                    </div>
                </div>
            </div>
        `;
    });
    
    container.innerHTML = html;
}

// Charger le graphique des revenus
async function loadRevenueChart() {
    try {
        const response = await apiRequest('operator/analytics');
        const revenueData = response.analytics?.revenue_trends || [];
        
        const ctx = document.getElementById('revenueChart').getContext('2d');
        
        // Détruire le graphique existant s'il existe
        if (revenueChart) {
            revenueChart.destroy();
        }
        
        revenueChart = new Chart(ctx, {
            type: 'line',
            data: {
                labels: revenueData.map(item => formatDate(item.date)),
                datasets: [{
                    label: 'Revenus (FCFA)',
                    data: revenueData.map(item => item.revenue),
                    borderColor: '#0d6efd',
                    backgroundColor: 'rgba(13, 110, 253, 0.1)',
                    tension: 0.4,
                    fill: true
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        display: false
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        ticks: {
                            callback: function(value) {
                                return formatCurrency(value);
                            }
                        }
                    }
                }
            }
        });
        
    } catch (error) {
        console.error('Erreur lors du chargement du graphique:', error);
        document.getElementById('revenueChart').parentElement.innerHTML = `
            <h5 class="mb-3"><i class="fas fa-chart-line me-2"></i>Évolution des revenus (7 derniers jours)</h5>
            <div class="text-center py-4">
                <i class="fas fa-exclamation-triangle fa-2x text-warning mb-3"></i>
                <p class="text-muted">Erreur lors du chargement du graphique</p>
            </div>
        `;
    }
}

// Gestion des sections
function showSection(sectionName) {
    // Masquer toutes les sections
    document.querySelectorAll('.content-section').forEach(section => {
        section.style.display = 'none';
    });
    
    // Retirer la classe active de tous les liens
    document.querySelectorAll('.sidebar .nav-link').forEach(link => {
        link.classList.remove('active');
    });
    
    // Afficher la section demandée
    document.getElementById(sectionName + 'Section').style.display = 'block';
    
    // Ajouter la classe active au lien correspondant
    event.target.classList.add('active');
    
    // Mettre à jour le titre
    const titles = {
        'dashboard': 'Tableau de bord',
        'bookings': 'Gestion des réservations',
        'trips': 'Gestion des voyages',
        'buses': 'Gestion des bus',
        'reports': 'Rapports et analyses',
        'validation': 'Validation des tickets'
    };
    
    document.getElementById('pageTitle').textContent = titles[sectionName] || 'EasyBus Opérateur';
    currentSection = sectionName;
}

// Configuration de la validation des tickets
function setupTicketValidation() {
    // Gérer la touche Entrée dans le champ de saisie
    document.getElementById('ticketCodeInput').addEventListener('keypress', function(e) {
        if (e.key === 'Enter') {
            validateTicket();
        }
    });
}

// Valider un ticket
async function validateTicket() {
    const ticketCode = document.getElementById('ticketCodeInput').value.trim();
    const resultContainer = document.getElementById('validationResult');
    
    if (!ticketCode) {
        showAlert('Veuillez saisir un code de ticket', 'warning');
        return;
    }
    
    try {
        // Afficher l'état de chargement
        resultContainer.innerHTML = `
            <div class="text-center">
                <i class="fas fa-spinner fa-spin fa-2x text-primary mb-3"></i>
                <p>Validation en cours...</p>
            </div>
        `;
        
        // Appel API pour valider le ticket
        const response = await apiRequest('tickets/validate', {
            method: 'POST',
            body: JSON.stringify({
                ticket_code: ticketCode
            })
        });
        
        // Afficher le résultat de la validation
        resultContainer.innerHTML = `
            <div class="alert alert-success">
                <div class="d-flex align-items-center">
                    <i class="fas fa-check-circle fa-2x me-3"></i>
                    <div>
                        <h5 class="mb-1">Ticket validé avec succès !</h5>
                        <p class="mb-1"><strong>Passager :</strong> ${response.passenger_name}</p>
                        <p class="mb-0"><strong>Siège :</strong> ${response.seat_number}</p>
                    </div>
                </div>
            </div>
        `;
        
        // Vider le champ de saisie
        document.getElementById('ticketCodeInput').value = '';
        document.getElementById('ticketCodeInput').focus();
        
        showAlert('Ticket validé avec succès', 'success');
        
    } catch (error) {
        console.error('Erreur lors de la validation:', error);
        
        resultContainer.innerHTML = `
            <div class="alert alert-danger">
                <div class="d-flex align-items-center">
                    <i class="fas fa-times-circle fa-2x me-3"></i>
                    <div>
                        <h5 class="mb-1">Validation échouée</h5>
                        <p class="mb-0">${error.message}</p>
                    </div>
                </div>
            </div>
        `;
        
        showAlert(error.message, 'danger');
    }
}

// Scanner QR Code (fonctionnalité future)
function startQRScanner() {
    showAlert('Scanner QR Code - Fonctionnalité en cours de développement', 'info');
}

// Afficher l'historique de validation
function showValidationHistory() {
    showAlert('Historique de validation - Fonctionnalité en cours de développement', 'info');
}

// Voir les détails d'un voyage
function viewTripDetails(tripId) {
    showAlert(`Détails du voyage #${tripId} - Fonctionnalité en cours de développement`, 'info');
}

// Fonctions utilitaires
function formatCurrency(amount) {
    return new Intl.NumberFormat('fr-FR', {
        style: 'decimal',
        minimumFractionDigits: 0
    }).format(amount) + ' FCFA';
}

function formatDate(dateString) {
    const date = new Date(dateString);
    return date.toLocaleDateString('fr-FR', {
        day: '2-digit',
        month: '2-digit'
    });
}

function formatTime(dateString) {
    const date = new Date(dateString);
    return date.toLocaleTimeString('fr-FR', {
        hour: '2-digit',
        minute: '2-digit'
    });
}

function getStatusText(status) {
    const statusMap = {
        'scheduled': 'Programmé',
        'ongoing': 'En cours',
        'completed': 'Terminé',
        'cancelled': 'Annulé'
    };
    return statusMap[status] || status;
}

// Fonction pour faire des requêtes API (réutilisée depuis auth.js)
async function apiRequest(endpoint, options = {}) {
    const url = `${API_BASE_URL}/${endpoint}`;
    const token = getAuthToken();
    
    const defaultOptions = {
        headers: {
            'Content-Type': 'application/json',
            ...(token && { 'Authorization': `Bearer ${token}` })
        }
    };
    
    const finalOptions = {
        ...defaultOptions,
        ...options,
        headers: {
            ...defaultOptions.headers,
            ...options.headers
        }
    };
    
    try {
        const response = await fetch(url, finalOptions);
        const data = await response.json();
        
        if (!response.ok) {
            throw new Error(data.message || 'Erreur de requête');
        }
        
        return data;
    } catch (error) {
        console.error('Erreur API:', error);
        throw error;
    }
}

// Fonction pour afficher les alertes
function showAlert(message, type = 'success') {
    const alertContainer = document.getElementById('alertContainer');
    const alertId = 'alert-' + Date.now();
    
    const alertHTML = `
        <div class="alert alert-${type} alert-dismissible fade show" role="alert" id="${alertId}">
            <i class="fas fa-${type === 'success' ? 'check-circle' : type === 'danger' ? 'exclamation-triangle' : 'info-circle'} me-2"></i>
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    `;
    
    alertContainer.insertAdjacentHTML('beforeend', alertHTML);
    
    // Auto-suppression après 5 secondes
    setTimeout(() => {
        const alert = document.getElementById(alertId);
        if (alert) {
            alert.remove();
        }
    }, 5000);
}

// Fonction de déconnexion
function logout() {
    if (confirm('Êtes-vous sûr de vouloir vous déconnecter ?')) {
        removeAuthToken();
        localStorage.removeItem('userData');
        window.location.href = 'index.html';
    }
}
