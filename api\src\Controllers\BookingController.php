<?php
require_once __DIR__ . '/../Models/BookingModel.php';
require_once __DIR__ . '/../Models/BookedSeatModel.php';
require_once __DIR__ . '/../Models/TicketModel.php';
require_once __DIR__ . '/../Models/UserModel.php';
require_once __DIR__ . '/../Controllers/NotificationController.php';
require_once __DIR__ . '/../Helpers/response.php';
require_once __DIR__ . '/../Helpers/validate.php';
require_once __DIR__ . '/../Middlewares/AuthMiddleware.php';

class BookingController {
    private $bookingModel;
    private $bookedSeatModel;
    private $ticketModel;
    private $userModel;
    private $notificationController;

    public function __construct() {
        $this->bookingModel = new BookingModel();
        $this->bookedSeatModel = new BookedSeatModel();
        $this->ticketModel = new TicketModel();
        $this->userModel = new UserModel();
        $this->notificationController = new NotificationController();
    }

    public function getUserBookings() {
        $user_id = checkAuth();
        $bookings = $this->bookingModel->getUserBookings($user_id);
        sendResponse(200, ['bookings' => $bookings]);
    }

    public function getBookingById($id) {
        $user_id = checkAuth();
        $booking = $this->bookingModel->getBookingById($id, $user_id);
        if ($booking) {
            sendResponse(200, ['booking' => $booking]);
        } else {
            sendResponse(404, ['message' => 'Réservation non trouvée']);
        }
    }

    public function cancelBooking($id) {
        $user_id = checkAuth();
        $affected = $this->bookingModel->cancelBooking($id, $user_id);
        if ($affected) {
            sendResponse(200, ['message' => 'Réservation annulée']);
        } else {
            sendResponse(404, ['message' => 'Réservation non trouvée ou non autorisée']);
        }
    }

    public function createBooking() {
        $user_id = checkAuth();

        $data = json_decode(file_get_contents('php://input'), true);
        validateFields($data, [
            'trip_id' => 'positiveInt',
            'boarding_stop_id' => 'positiveInt',
            'dropping_stop_id' => 'positiveInt',
            'total_amount' => 'positiveInt',
            'seat_ids' => 'required' // Nouveau : liste des sièges à réserver
        ]);

        // Vérifier que seat_ids est un tableau d’entiers positifs
        if (!is_array($data['seat_ids']) || empty($data['seat_ids'])) {
            sendResponse(400, ['message' => 'seat_ids doit être un tableau non vide d’entiers positifs']);
            return;
        }
        foreach ($data['seat_ids'] as $seat_id) {
            Validator::positiveInt($seat_id, 'seat_ids');
        }

        $data['user_id'] = $user_id;
        $booking_id = $this->bookingModel->createBooking($data);

        // Réserver les sièges
        foreach ($data['seat_ids'] as $seat_id) {
            $this->bookedSeatModel->createBookedSeat([
                'seat_id' => $seat_id,
                'trip_id' => $data['trip_id'],
                'booking_id' => $booking_id,
                'user_id' => $user_id
            ]);
        }

        sendResponse(201, ['message' => 'Réservation créée', 'booking_id' => $booking_id]);
    }

    /**
     * Créer une réservation sans compte utilisateur
     * POST /v1/bookings/guest
     */
    public function createGuestBooking($params = []) {
        try {
            $data = !empty($params) ? $params : json_decode(file_get_contents('php://input'), true);

            validateFields($data, [
                'trip_id' => 'positiveInt',
                'boarding_stop_id' => 'positiveInt',
                'dropping_stop_id' => 'positiveInt',
                'total_amount' => 'positiveInt',
                'seat_ids' => 'required',
                'passengers' => 'required',
                'contact_email' => 'email',
                'contact_phone' => 'required'
            ]);

            // Vérifications similaires à createBooking
            if (!is_array($data['seat_ids']) || empty($data['seat_ids'])) {
                sendResponse(400, ['message' => 'seat_ids doit être un tableau non vide']);
                return;
            }

            if (!is_array($data['passengers']) || empty($data['passengers'])) {
                sendResponse(400, ['message' => 'passengers doit être un tableau non vide']);
                return;
            }

            if (count($data['passengers']) !== count($data['seat_ids'])) {
                sendResponse(400, ['message' => 'Le nombre de passagers doit correspondre au nombre de sièges']);
                return;
            }

            // Créer un utilisateur temporaire ou utiliser un ID spécial pour les invités
            $guestUserId = $this->createOrGetGuestUser($data['contact_email'], $data['contact_phone']);
            $data['user_id'] = $guestUserId;

            $booking_id = $this->bookingModel->createGuestBooking($data);

            // Créer les tickets et réserver les sièges
            $tickets = [];
            foreach ($data['seat_ids'] as $index => $seat_id) {
                $this->bookedSeatModel->createBookedSeat([
                    'seat_id' => $seat_id,
                    'trip_id' => $data['trip_id'],
                    'booking_id' => $booking_id,
                    'user_id' => $guestUserId
                ]);

                $passenger = $data['passengers'][$index];
                $ticketCode = $this->ticketModel->generateTicketCode();

                $ticketId = $this->ticketModel->createTicket([
                    'booking_id' => $booking_id,
                    'trip_id' => $data['trip_id'],
                    'seat_id' => $seat_id,
                    'passenger_name' => $passenger['name'],
                    'passenger_email' => $data['contact_email'],
                    'passenger_phone' => $data['contact_phone'],
                    'ticket_code' => $ticketCode
                ]);

                $tickets[] = [
                    'ticket_id' => $ticketId,
                    'ticket_code' => $ticketCode,
                    'passenger_name' => $passenger['name'],
                    'seat_number' => $seat_id
                ];
            }

            // Envoyer l'email de confirmation
            $this->sendGuestBookingConfirmationEmail($booking_id, $tickets, $data);

            sendResponse(201, [
                'message' => 'Réservation créée avec succès',
                'booking_id' => $booking_id,
                'tickets' => $tickets,
                'search_info' => [
                    'email' => $data['contact_email'],
                    'phone' => $data['contact_phone']
                ]
            ]);

        } catch (Exception $e) {
            sendResponse(500, ['message' => 'Erreur serveur', 'error' => $e->getMessage()]);
        }
    }

    /**
     * Rechercher des réservations par email ou téléphone
     * GET /v1/bookings/search?email=...&phone=...
     */
    public function searchBookings($params = []) {
        try {
            $email = $params['email'] ?? $_GET['email'] ?? '';
            $phone = $params['phone'] ?? $_GET['phone'] ?? '';

            if (empty($email) && empty($phone)) {
                sendResponse(400, ['message' => 'Email ou téléphone requis pour la recherche']);
                return;
            }

            $bookings = $this->bookingModel->searchBookingsByContact($email, $phone);

            if (empty($bookings)) {
                sendResponse(404, ['message' => 'Aucune réservation trouvée']);
                return;
            }

            // Enrichir les données avec les tickets
            foreach ($bookings as &$booking) {
                $booking['tickets'] = $this->ticketModel->getTicketsByBooking($booking['booking_id']);
            }

            sendResponse(200, ['bookings' => $bookings]);

        } catch (Exception $e) {
            sendResponse(500, ['message' => 'Erreur serveur', 'error' => $e->getMessage()]);
        }
    }

    /**
     * Mettre à jour une réservation
     * PUT /v1/bookings/{id}
     */
    public function updateBooking($params) {
        try {
            $user = AuthMiddleware::authenticate();
            $bookingId = $params['id'] ?? 0;

            if (!$bookingId) {
                sendResponse(400, ['message' => 'ID réservation requis']);
                return;
            }

            $data = json_decode(file_get_contents('php://input'), true);

            // Vérifier que la réservation appartient à l'utilisateur
            $booking = $this->bookingModel->getBookingById($bookingId, $user->sub);
            if (!$booking) {
                sendResponse(404, ['message' => 'Réservation non trouvée']);
                return;
            }

            $success = $this->bookingModel->updateBooking($bookingId, $data, $user->sub);

            if ($success) {
                sendResponse(200, ['message' => 'Réservation mise à jour']);
            } else {
                sendResponse(500, ['message' => 'Erreur lors de la mise à jour']);
            }

        } catch (Exception $e) {
            sendResponse(500, ['message' => 'Erreur serveur', 'error' => $e->getMessage()]);
        }
    }

    /**
     * Créer ou récupérer un utilisateur invité
     */
    private function createOrGetGuestUser($email, $phone) {
        // Vérifier si un utilisateur avec cet email existe déjà
        $existingUser = $this->userModel->findByEmailOrPhone($email);

        if ($existingUser) {
            return $existingUser['user_id'];
        }

        // Créer un nouvel utilisateur invité
        $userData = [
            'email' => $email,
            'phone' => $phone,
            'first_name' => 'Invité',
            'last_name' => '',
            'password_hash' => null, // Pas de mot de passe pour les invités
            'verification_status' => 'pending',
            'status' => 'active'
        ];

        return $this->userModel->createGuestUser($userData);
    }

    /**
     * Envoyer l'email de confirmation pour une réservation normale
     */
    private function sendBookingConfirmationEmail($bookingId, $tickets) {
        try {
            $booking = $this->bookingModel->getBookingDetailsForEmail($bookingId);

            if ($booking && !empty($booking['passenger_email'])) {
                $this->notificationController->sendBookingConfirmation($booking, $tickets);
            }
        } catch (Exception $e) {
            error_log('Erreur envoi email confirmation: ' . $e->getMessage());
        }
    }

    /**
     * Envoyer l'email de confirmation pour une réservation invité
     */
    private function sendGuestBookingConfirmationEmail($bookingId, $tickets, $data) {
        try {
            $booking = $this->bookingModel->getBookingDetailsForEmail($bookingId);

            if ($booking) {
                $booking['passenger_email'] = $data['contact_email'];
                $booking['passenger_name'] = $data['passengers'][0]['name'] ?? 'Invité';

                $this->notificationController->sendBookingConfirmation($booking, $tickets);
            }
        } catch (Exception $e) {
            error_log('Erreur envoi email confirmation invité: ' . $e->getMessage());
        }
    }
}