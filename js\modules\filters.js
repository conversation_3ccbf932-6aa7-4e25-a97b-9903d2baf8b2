/**
 * Module for filtering and sorting trips
 */
import { parseDuration } from './formatUtils.js';
import { fetchAmenities } from './api.js';

/**
 * Filters trips based on criteria
 * @param {Array} trips - Array of trips to filter
 * @param {string} busType - Bus type filter
 * @param {Array} amenities - Required amenities
 * @param {number} maxPrice - Maximum price
 * @returns {Array} - Filtered trips
 */
export function filterTrips(trips, busType, amenities, maxPrice) {
  return trips.filter(trip => {
    const tripBusType = trip.bus_type;
    const tripPrice = Math.min(...Object.values(trip.pricing).map(Number));
    const tripAmenities = trip.bus_amenities.map(a => a.amenity_name);

    let matches = true;

    if (busType && tripBusType !== busType) {
      matches = false;
    }

    if (amenities.length > 0) {
      const hasAllAmenities = amenities.every(amenity => tripAmenities.includes(amenity));
      if (!hasAllAmenities) matches = false;
    }

    if (tripPrice > maxPrice) {
      matches = false;
    }

    return matches;
  });
}

/**
 * Sorts trips based on criteria
 * @param {Array} trips - Array of trips to sort
 * @param {string} sortBy - Sort criterion
 * @param {string} sortOrder - Sort order (asc/desc)
 * @returns {Array} - Sorted trips
 */
export function sortTrips(trips, sortBy, sortOrder) {
  return trips.sort((a, b) => {
    let valueA, valueB;

    switch (sortBy) {
      case "departureTime":
        valueA = Date.parse(a.departure_time);
        valueB = Date.parse(b.departure_time);
        break;
      case "arrivalTime":
        valueA = Date.parse(a.arrival_time);
        valueB = Date.parse(b.arrival_time);
        break;
      case "price":
        valueA = Math.min(...Object.values(a.pricing).map(Number));
        valueB = Math.min(...Object.values(b.pricing).map(Number));
        break;
      case "duration":
        valueA = parseDuration(a.duration);
        valueB = parseDuration(b.duration);
        break;
      default:
        return 0;
    }

    if (sortOrder === "asc") {
      return valueA - valueB;
    } else {
      return valueB - valueA;
    }
  });
}

/**
 * Loads amenities into the filter modal
 * @returns {Promise<void>}
 */
export async function loadAmenities() {
  try {
    const amenities = await fetchAmenities();
    const amenitiesContainer = document.getElementById('amenities');
    
    amenitiesContainer.innerHTML = amenities.map(amenity => `
      <div class="form-check">
        <input class="form-check-input" type="checkbox" value="${amenity.amenity_name}" id="amenity-${amenity.amenity_id}">
        <label class="form-check-label" for="amenity-${amenity.amenity_id}">
          ${amenity.amenity_name}
        </label>
      </div>
    `).join('');
  } catch (error) {
    console.error("Erreur lors du chargement des commodités:", error);
  }
}

/**
 * Sets up filter event listeners
 * @param {Function} renderTrips - Function to render trips
 * @param {Array} allTrips - Array of all trips
 */
export function setupFilters(renderTrips, allTrips) {
  // Filter modal button
  document.querySelector('[data-bs-target="#filterModal"]').addEventListener('click', async function () {
    await loadAmenities();
  });

  // Price range slider
  document.getElementById('priceRange').addEventListener('input', function() {
    const value = this.value;
    document.getElementById('priceRangeValue').textContent = `0 - ${value} FCFA`;
  });

  // Apply filters button
  document.getElementById("applyFilters").addEventListener("click", async function () {
    const busType = document.getElementById("busType").value;
    const selectedAmenities = Array.from(document.querySelectorAll("#amenities input:checked")).map(input => input.value);
    const priceRange = parseFloat(document.getElementById("priceRange").value);

    const filterModal = bootstrap.Modal.getInstance(document.getElementById("filterModal"));
    filterModal.hide();

    const filteredTrips = filterTrips(allTrips, busType, selectedAmenities, priceRange);
    renderTrips(filteredTrips);
  });

  // Apply sort button
  document.getElementById("applySort").addEventListener("click", function () {
    const sortBy = document.getElementById("sortBy").value;
    const sortOrder = document.getElementById("sortOrder").value;

    const sortModal = bootstrap.Modal.getInstance(document.getElementById("sortModal"));
    sortModal.hide();

    const sortedTrips = sortTrips([...allTrips], sortBy, sortOrder);
    renderTrips(sortedTrips);
  });
}
